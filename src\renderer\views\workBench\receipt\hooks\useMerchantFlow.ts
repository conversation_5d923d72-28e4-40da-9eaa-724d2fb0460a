import { useMerchantStore } from '@renderer/store/modules/merchant';
import { StatusType } from '@renderer/api/workBench/merchant.model';
import { computed } from 'vue';
import { useWorkBenchNavigate } from './useNavigate';

/**
 * 商户入网流程
 */
function useMerchantFlow() {
  const merchantStore = useMerchantStore();
  const { toMerchantApply, toMerchantSplit, toMerchantRealName } = useWorkBenchNavigate();

  // 是否已完成入网
  const isMerchantFlowReady = computed(() => merchantStore.isExist
    // 开通商户
    && merchantStore.status?.is_open_merchant === '2'
    // 电子合同申请通过
    && merchantStore.status?.apply_status === '2'
    // 支付宝实名
    && merchantStore.status?.is_alipay_merchant === '1'
    // 微信实名
    && merchantStore.status?.is_wechat_merchant === '1'
    // 分账成功
    && merchantStore.status?.is_separate_accounts === '2'
    // 分佣已开通
    && merchantStore.status?.is_receiver_no === '1');

  // 是否已完成分佣
  const isCommissionFlowReady = computed(() => merchantStore.isExist && merchantStore.status?.is_receiver_no === '1');

  /**
   * 商户状态推导
   */
  const resolveMerchantStepStatus = (isCommission = false): StatusType => {
    if (!merchantStore.isExist) return 'startOpen';

    const {
      is_separate_accounts,
      apply_status,
      is_alipay_merchant,
      is_wechat_merchant,
      is_open_merchant,
      is_receiver_no,
    } = merchantStore.status || {};

    // 分佣流程优先判断
    if (isCommission) {
      if (is_receiver_no === '1') return 'commissionSuccess'; // 分佣已开通
      if (is_receiver_no === '0') return 'signSuccess'; // 分佣未开通/待开通
      // 可根据实际业务补充更多状态
    }

    // 统一实名状态
    const isAlipayVerified = is_alipay_merchant === '1';
    const isWechatVerified = is_wechat_merchant === '1';
    const isRealNameVerified = isAlipayVerified && isWechatVerified; // 全部实名
    const isPartialRealName = isAlipayVerified || isWechatVerified; // 部分实名
    const isRealNamePending = apply_status === '2' && isPartialRealName && !isRealNameVerified; // 实名中

    // 优先级顺序：分账 > 实名 > 签约 > 入网 > 成功

    // 1. 分账
    if (is_separate_accounts === '2') return 'splitSuccess'; // 分账成功
    if (is_separate_accounts === '3') return 'splitFailed'; // 分账失败
    if (is_separate_accounts === '1') return 'splitPending'; // 审核中

    // 2. 实名
    if (is_open_merchant === '3') return 'failed'; // 入网失败
    if (!isCommission) {
      if (isRealNameVerified && is_separate_accounts === '0') return 'realNameSuccess'; // 实名成功
      if (isRealNamePending || isPartialRealName) return 'realNamePending'; // 实名中
    }

    // 3. 签约
    if (is_open_merchant === '2') {
      if (apply_status === '1') return 'needSign'; // 待签约
      if (apply_status === '2') return 'signSuccess'; // 签约成功
      if (apply_status === '3') return 'failed'; // 入网失败
    }

    // 3.1 重新入网
    if (is_open_merchant === '0' && apply_status === '1') return 'needSign'; // 重新入网-签约中

    // 4. 入网
    if (apply_status === '0') {
      if (is_open_merchant === '0') return 'startOpen'; // 填写入网信息
      if (is_open_merchant === '1') return 'pending'; // 入网审核中
      if (is_open_merchant === '3') return 'failed'; // 入网失败
    }

    // 5. 入网成功
    if (isMerchantFlowReady.value) return 'success'; // 入网成功

    // 极端情况兜底
    return 'startOpen';
  };

  // 主流程判断
  const checkMerchantFlow = async (teamId, isCommission = 'false') => {
    await merchantStore.checkExist(teamId);

    // 未入网，进入入网申请
    if (!merchantStore.isExist) {
      toMerchantApply({ teamId, isCommission });
      return;
    }

    // 获取最新商户状态
    await merchantStore.getStatus(teamId);
    const statusType = resolveMerchantStepStatus(isCommission === 'true');

    switch (statusType) {
      case 'startOpen':
        toMerchantApply({ teamId, isCommission });
        break;
      case 'pending':
        toMerchantApply({ status: 'pending', teamId, isCommission });
        break;
      case 'splitPending':
      case 'splitSuccess':
      case 'splitFailed':
        toMerchantSplit({ status: statusType, teamId, isCommission });
        break;
      case 'signSuccess':
        toMerchantApply({ status: 'signSuccess', teamId, isCommission });
        break;
      case 'failed':
        toMerchantApply({ status: 'failed', teamId, isCommission });
        break;
      case 'needSign':
        toMerchantApply({ status: 'needSign', teamId, isCommission });
        break;
      case 'realNamePending':
        // 跳过实名认证，直接进入分账流程
        if (isCommission === 'true') {
          toMerchantSplit({ teamId, isCommission });
        } else {
          toMerchantRealName({ teamId, isCommission });
        }
        break;
      case 'realNameSuccess':
        toMerchantSplit({ teamId, isCommission });
        break;
      case 'success':
        toMerchantSplit({ status: 'success', teamId, isCommission });
        break;
      case 'commissionSuccess':
        // 分佣已开通，跳转到分佣成功页面或提示
        toMerchantSplit({ status: 'commissionSuccess', teamId, isCommission: 'true' });
        break;
      case 'commissionPending':
        // 分佣未开通，跳转到分佣开通流程
        toMerchantSplit({ status: 'commissionPending', teamId, isCommission: 'true' });
        break;
      default:
        toMerchantApply({ teamId, isCommission });
        break;
    }
  };

  // 统一的本地状态推导方法
  const getMerchantStepStatus = (isCommission = false): StatusType => resolveMerchantStepStatus(isCommission);

  // 申请按钮逻辑合并
  const handleApplyClickFlow = async ({
    teamId,
    fetchTeamAuthStatus,
    orgAuthVisible,
    loading,
    isCommission = 'false',
  }) => {
    // eslint-disable-next-line no-param-reassign
    loading.value = true;
    const needAuth = await fetchTeamAuthStatus();
    // eslint-disable-next-line no-param-reassign
    loading.value = false;

    if (needAuth) {
      // eslint-disable-next-line no-param-reassign
      orgAuthVisible.value = true;
      return;
    }

    // D、若为已认证，判断是否已经商户入网
    await checkMerchantFlow(teamId, isCommission);
  };

  // 初始化流程：设置 teamId、检查商户、获取状态
  const initMerchantFlow = async (teamId) => {
    merchantStore.setTeamId(teamId.value);

    await merchantStore.checkExist();
    if (merchantStore.isExist) {
      await merchantStore.getStatus();
    }
  };

  return {
    isMerchantFlowReady,
    isCommissionFlowReady,
    checkMerchantFlow,
    handleApplyClickFlow,
    initMerchantFlow,
    getMerchantStepStatus,
  };
}

export default useMerchantFlow;
