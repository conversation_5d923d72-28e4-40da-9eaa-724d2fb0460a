import { shop } from '@/assets/svg/shop.svg';
import { ClientSide } from '@renderer/types/enumer.js';
import { useI18n } from 'vue-i18n';
import { useServiceStore } from '@renderer/views/service/store/service';
import { useProjectStore } from '@renderer/views/engineer/store/engineer';
import { TextareaValue } from 'tdesign-vue-next';
import appicon1 from '@/assets/bench/appicon1.svg';
import appicon2 from '@/assets/bench/appicon2.svg';
import appicon3 from '@/assets/bench/appicon3.svg';
import appicon4 from '@/assets/bench/appicon4.svg';
import appicon5 from '@/assets/bench/appicon5.svg';
import appicon6 from '@/assets/bench/appicon6.svg';
import appicon7 from '@/assets/bench/appicon7.svg';
import appicon8 from '@/assets/bench/appicon8.svg';
import appicon9 from '@/assets/bench/appicon9.svg';
import appicon10 from '@/assets/bench/appicon10.svg';
import appicon11 from '@/assets/bench/appicon11.svg';
import appicon12 from '@/assets/bench/appicon12.svg';
import appicon13 from '@/assets/bench/appicon13.svg';
import appicon14 from '@/assets/bench/appicon14.svg';
import appicon15 from '@/assets/pb/party.svg';
import ggao from '@/assets/niche/ggao.svg';
import policy from '@/assets/bench/policy.svg';
import ztcnew from '@/assets/bench/ztcnew.svg';

import fengcai from '@/assets/fengcai/fc.svg';
import CBD from '@/assets/partner/cbd.svg';
import partner from '@/assets/partner/partner2.svg';
import association from '@/assets/bench/association.svg';
import shopIcon from '@/assets/bench/shop.svg';

import { goSquareModule } from '@/views/square/utils/business';
import { i18n } from '@/i18n';
import LynkerSDK from '@renderer/_jssdk';
import core from '@lynker-desktop/electron-sdk/renderer';

const { shell, ipcRenderer } = LynkerSDK;

const { t } = i18n.global;

export const filterImg = (val) => {
  const imgMap = {
    disk: { img: appicon4, name: t('application.disk') },
    approve: { img: appicon1, name: t('application.approve') },
    square: { img: appicon6, name: t('application.square') },
    member: { img: appicon7, name: t('application.member') },
    niche: { img: appicon2, name: t('application.niche') },
    store: { img: shopIcon, name: t('application.shop') },
    serve: { img: appicon3, name: t('application.serve') },
    activities: { img: appicon5, name: t('application.activities') },
    cbd: { img: CBD, name: t('application.digital_cbd') },
    government: { img: appicon8, name: t('application.government') },
    kefu: { img: appicon9, name: t('application.kefu') },
    notice: { img: ggao, name: '公告' },
    policy: { img: ztcnew, name: t('policy.policy_express') },

    'chain-device': { img: appicon10, name: t('application.chain-device') },
    'chain-partners': { img: appicon11, name: t('application.chain-partners') },
    'chain-supplier': { img: appicon12, name: t('supplier.supplier_title2') },
    'chain-customer': { img: appicon13, name: t('customer.customer_title2') },
    project: { img: appicon14, name: '工程' },
    'society-article': { img: appicon15, name: t('square.partyBuild') },
    'platform-view': { img: fengcai, name: t('banch.ptfc') },
    'new-partner': { img: partner, name: '合伙人' },
    association: { img: association, name: t('association.workbench.a') },
  };

  return imgMap[val.uuid] || {};
};
export const openAppListFn = (item, team, query?) => {
  if (item.uuid === 'square') {
    goSquareModule(
      {
        redirect: '',
        teamId: team.teamId,
        toOrg: 'true',
      },
      { toOrg: true },
    );
    return;
  }
  if (item.uuid === 'activities') {
    window.localStorage.setItem('activityTeamId', team.teamId);
    ipcRenderer.invoke('click-menu-item', {
      url: '/activity/activityList',
      workBenchJumpTeamId: team.teamId,
    });
    ipcRenderer.send('update-nume-index', 'activities');
    return;
  }
  if (item.uuid === 'disk') {
    ipcRenderer.invoke('click-menu-item', {
      url: '/clouddiskIndex/clouddiskhome',
      workBenchJumpTeamId: team.teamId, // 跳转到云盘的组织id
    });
    ipcRenderer.send('update-nume-index', 'disk');
    return;
  }

  const appMap = {
    disk: {
      path: '/clouddiskIndex/clouddiskhome',
      type: null,
      teamId: team.teamId,
    },
    'chain-customer': {
      path: '/workBenchIndex/customer-list',
      type: ClientSide.CUSTOMER,
      teamIdKey: 'customerTeamId',
      routeName: 'customerList',
    },
    approve: {
      path: '/workBenchIndex/approve_home',
      type: ClientSide.APPROVAL,
      teamIdKey: 'approvalteamid',
      routeName: 'bench_approve_home',
      query: { ...query },
    },
    policy: {
      path: '/workBenchIndex/policy-express',
      type: ClientSide.POLICYTHROUGHTRAIN,
      routeName: 'PolicyExpress',
    },
    notice: {
      path: '/workBenchIndex/notice',
      type: ClientSide.NOTICE,
      routeName: 'notice',
    },
    square: {
      handler: goSquareModule,
    },
    member: {
      path: '/workBenchIndex/member_home',
      type: ClientSide.MEMBER,
      routeName: 'bench_member_home',
      query: { platform: 'digital_workbench', ...team, ...query },
    },
    niche: {
      path: '/workBenchIndex/nicheHome',
      type: ClientSide.NICHE,
      teamIdKey: 'businessTeamId',
      routeName: 'nicheHome',
    },
    'society-article': {
      path: '/workBenchIndex/pb_list',
      type: ClientSide.PB,
      routeName: 'bench_pb_list',
      title: t('square.partyBuild'),
      teamId: team.teamId,
      query: {
        team_id: team.teamId,
      },
    },
    'platform-view': {
      path: '/workBenchIndex/fengcai_list',
      type: ClientSide.FC,
      routeName: 'bench_fengcai_list',
      title: t('banch.ptfc'),
      teamId: team.teamId,
      query: {
        team_id: team.teamId,
      },
    },
    serve: {
      path: '/workBenchIndex/service_home',
      type: ClientSide.SERVICE,
      routeName: 'bench_service_home',

      setActiveAccount: useServiceStore().setActiveAccount,
    },
    government: {
      path: '/workBenchIndex/politics_home',
      query: { platform: 'digital_workbench', ...team, ...query },
      routeName: 'bench_politics_home',
      type: ClientSide.POLITICS,
    },
    cbd: {
      path: '/workBenchIndex/cbd_home',
      query: { platform: 'digital_workbench', ...team, ...query },
      routeName: 'bench_cbd_home',
      type: ClientSide.CBD,
    },

    association: {
      path: '/workBenchIndex/association_home',
      query: { platform: 'digital_workbench', ...team, ...query },
      routeName: 'bench_association_home',
      type: ClientSide.ASSOCIATION,
    },
    img: CBD,
    name: t('application.digital_cbd'),
    kefu: {
      path: '/workBenchIndex/customerServiceList',
      type: ClientSide.KEFU,
      teamIdKey: 'approvalteamid',
      routeName: 'customerServiceList',
    },
    'chain-device': {
      path: '/workBenchIndex/device-list',
      type: ClientSide.DEVICE,
      teamIdKey: 'deviceTeamId',
      routeName: 'deviceList',
    },
    'chain-partners': {
      path: '/workBenchIndex/partner-list',
      type: ClientSide.PARTNER,
      teamIdKey: 'partnerTeamId',
      routeName: 'partnerList',
    },
    'chain-supplier': {
      path: '/workBenchIndex/supplier-list',
      type: ClientSide.SUPPLIER,
      teamIdKey: 'supplierTeamId',
      routeName: 'supplierList',
    },
    project: {
      path: '/workBenchIndex/engineer_home',
      type: ClientSide.EGINEER,
      setActiveAccount: useProjectStore().setActiveAccount,
      routeName: 'bench_engineer_home',
    },
    'new-partner': {
      path: '/workBenchIndex/partnerHome/index',
      type: ClientSide.PARTNERAPP,
      teamIdKey: 'businessTeamId',
      routeName: 'partnerHome',
    },
  };

  if (appMap[item.uuid]) {
    const { path, type, teamId, query, handler, teamIdKey, routeName, setActiveAccount } = appMap[item.uuid];
    if (path) {
      const objs = {
        path,
        query: query || {},
      };

      const ipcObj = {
        type,
        path,
        query: query || {},
        name: routeName,
        path_uuid: item.uuid,
        title: item.name,
        ...objs,
      };

      if (teamId) {
        ipcObj.teamId = teamId;
      }

      if (setActiveAccount) {
        setActiveAccount(team);
      }
      if (ipcObj.query?.user_ids) {
        ipcObj.query.user_ids = JSON.stringify(ipcObj.query?.user_ids);
      }
      console.log(ipcObj, 'ipcObjjjjj');
      ipcRenderer.invoke('set-work-bench-tab-item', ipcObj);

      if (teamIdKey) {
        window.localStorage.setItem(teamIdKey, team.teamId);
      }

      return objs;
    }
    if (handler) {
      handler({ teamId });
    }
  }
};

export const preventEnter = (value: TextareaValue, context: { e: KeyboardEvent }) => {
  if (context.e.key === 'Enter') {
    context.e.preventDefault();
  }
};

/**
 * 跳转到店铺
 * @param path 店铺路径
 * @param params 店铺参数 subMenuId:店铺tabId
 */
export const navigateToShop = (path = '', params  = {}, options = {}) => {
  const honorteam = JSON.parse(localStorage.getItem('honorteam') || '{}');
  const url = LynkerSDK.getH5UrlWithParams(`/shop/index.html#/${path}`, {
      cardId: honorteam.cardId,
      ...params
    })
  console.log('====>url',params,url)
  LynkerSDK.workBench.openTabForWebview({
    path_uuid: 'shop',
    title: t('application.shop'),
    url,
    query: params,
    icon: 'store',
    activeIcon: 'store',
    type: 31,
    ...options,
  });
}
