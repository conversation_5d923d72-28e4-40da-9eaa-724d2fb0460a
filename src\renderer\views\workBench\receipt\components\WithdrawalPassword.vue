<script setup lang='ts'>
import { ref, onMounted, computed } from 'vue';
import { getWithdrawalList } from '@renderer/api/workBench/merchant';
import { PasswordDialogMode, WithdrawalItem, WithdrawalPasswordPayload } from '@renderer/api/workBench/merchant.model';
import { to } from 'await-to-js';
import OrgAuth from '@renderer/components/orgAuth/orgAuth.vue';
import { useMerchantStore } from '@renderer/store/modules/merchant';
import { useRoute } from 'vue-router';
import { useWorkBenchNavigate } from '../hooks/useNavigate';
import LegalIdCardDialog from './LegalIdCardDialog.vue';
import PasswordDialog from './PasswordDialog.vue';
import { useTeamAuthStatus } from '../hooks/useMerchant';

const props = defineProps<{
  teamId: string;
}>();

const accounts = ref<WithdrawalItem[]>([]);
const loading = ref(false);
const route = useRoute();
const { toMerchantApply } = useWorkBenchNavigate();
const merchantStore = useMerchantStore();

const teamId = computed(() => {
  if (typeof props.teamId === 'string' && props.teamId) return props.teamId;
  if (typeof route.query?.teamId === 'string') return route.query.teamId;
  return '';
});

const {
  orgAuthDetail,
  orgAuthVisible,
  fetchTeamAuthStatus,
} = useTeamAuthStatus(teamId);

const legalIdCardVisible = ref(false);
const passwordDialogVisible = ref(false);
const currentAccount = ref<WithdrawalItem | null>(null);
const passwordDialogMode = ref<PasswordDialogMode>('set');
const legalIdCardCodeType = ref<PasswordDialogMode>('set');
const legalIdCardDialogRef = ref();

// 设置提现密码
const handleSetPassword = async (account: WithdrawalItem) => {
  loading.value = true;

  // 1. 检查组织认证
  const needAuth = await fetchTeamAuthStatus();
  if (needAuth) {
    loading.value = false;
    return;
  }

  // 2. 检查商户入网
  await merchantStore.getStatus(teamId.value);
  if (merchantStore.isNotOpened) {
    toMerchantApply({ teamId: teamId.value });
    loading.value = false;
    return;
  }

  // 3. 已入网，弹出法人身份证验证弹窗
  currentAccount.value = account;
  legalIdCardVisible.value = true;
  legalIdCardCodeType.value = 'set';
  passwordDialogMode.value = 'set';
  loading.value = false;
};

// 修改提现密码
const handleModifyPassword = (account: WithdrawalItem) => {
  currentAccount.value = account;
  passwordDialogMode.value = 'modify';
  passwordDialogVisible.value = true;
};

// 忘记提现密码（重置密码）
const handleForgotPassword = (account: WithdrawalItem) => {
  currentAccount.value = account;
  legalIdCardCodeType.value = 'reset';
  passwordDialogMode.value = 'reset';
  legalIdCardVisible.value = true;
};

// 法人身份证验证通过，打开设置提现密码弹窗
const handleLegalIdCardNextStep = (data: WithdrawalPasswordPayload) => {
  currentAccount.value = {
    ...currentAccount.value,
    ...data,
    // HACK id 会被 data.id 覆盖
    id: currentAccount.value?.id,
  };

  legalIdCardVisible.value = false;
  passwordDialogVisible.value = true;
};

// 打开法人身份证验证弹窗
const handlePasswordPrevStep = () => {
  passwordDialogVisible.value = false;
  legalIdCardVisible.value = true;
};

// 处理密码弹窗关闭
const handlePasswordClose = () => {
  legalIdCardDialogRef.value?.resetForm();
};

// 获取提现账户列表
const fetchAccounts = async () => {
  loading.value = true;
  const [err, res] = await to(getWithdrawalList(props.teamId));
  loading.value = false;
  if (err) return;

  accounts.value = res?.data?.data?.list || [];
};

onMounted(() => {
  fetchAccounts();
});
</script>

<template>
  <div class="page-wrap">
    <t-alert theme="info" class="mb-16!">请设置另可平台对应业务账户资金提现密码，用于提现认证</t-alert>

    <div v-loading="loading" class="account-list">
      <div v-for="account in accounts" :key="account.id" class="account-item">
        <div class="account-name">
          <t-image :src="account.logo_url" class="w-44 h-44 border-rd-8 bg-transparent!" />
          <div class="flex-1">
            <div class="font-600">{{ account.title }}</div>
            <div class="font-400">{{ account.desc }}</div>
          </div>
        </div>

        <div class="account-setting">
          <template v-if="account.password">
            <t-button theme="primary" variant="text" @click="handleModifyPassword(account)">
              修改提现密码
            </t-button>
            <t-button theme="primary" variant="text" @click="handleForgotPassword(account)">
              忘记提现密码
            </t-button>
          </template>

          <t-button
            v-else
            theme="primary"
            variant="text"
            @click="handleSetPassword(account)"
          >
            未设置
          </t-button>
        </div>
      </div>
    </div>

    <!-- 组织认证弹窗 -->
    <OrgAuth
      v-if="orgAuthVisible"
      v-model:visible="orgAuthVisible"
      :show-type="orgAuthDetail?.auth === 0 ? 'edit' : 'detail'"
      :region="orgAuthDetail?.region || 'CN'"
      :team-id="orgAuthDetail?.teamId || String(teamId)"
      :org-type="orgAuthDetail?.orgType"
      @success="orgAuthVisible = false"
    />

    <!-- 法人身份证实名验证弹窗 -->
    <LegalIdCardDialog
      ref="legalIdCardDialogRef"
      v-model:visible="legalIdCardVisible"
      :mode="legalIdCardCodeType"
      :account="currentAccount"
      @next-step="handleLegalIdCardNextStep"
    />

    <!-- 设置/修改提现密码弹窗 -->
    <PasswordDialog
      v-if="currentAccount"
      v-model:visible="passwordDialogVisible"
      :mode="passwordDialogMode"
      :account="currentAccount"
      @prev-step="handlePasswordPrevStep"
      @close="handlePasswordClose"
      @success="fetchAccounts"
    />
  </div>
</template>

<style lang='less' scoped>
.page-wrap {
  width: 100%;
  height: 100%;
}

.account-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.account-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #FFFFFF;
  border: 1px solid #ECEFF5;
  border-radius: 8px;

  .account-name {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 12px;
    color: #1A2139;
    font-size: 14px;
    line-height: 22px;
  }

  .account-setting {
    display: flex;
    gap: 8px;
    :deep(.t-button--theme-primary) {
      color: var(--kyy_color_switch_brand_default, #4D5EFF) !important;
    }
  }
}

:deep(.t-button--variant-text) {
  padding: 4px;
  color: var(--kyy_color_switch_brand_default, #4D5EFF);
  &:hover {
    background: var(--bg-kyy_color_bg_list_hover, #F3F6FA);
  }
}
</style>
