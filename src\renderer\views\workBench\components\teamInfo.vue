<template>
  <div class="team-info-box">
    <div class="container">
      <div class="footer">
        <div v-if="organiseType === 'look'" class="btns">
          <t-button class="minw83" theme="primary" @click="onUpdate">修改 </t-button>
        </div>
        <div v-else-if="organiseType === 'update'" class="btns">
          <t-button class="minw83" style="margin-right: 8px" theme="default" variant="outline" @click="onCancel"
            >取消</t-button
          >
          <t-button class="minw83" theme="primary" @click="onSave">保存</t-button>
        </div>
      </div>
      <t-space direction="vertical" class="bok" style="width: 100%; padding-top: 4px; padding-bottom: 16px">
        <t-form ref="form" :data="formData" :rules="FORM_RULES" label-align="top" :label-width="60">
          <div class="space">
            <div class="space-title">{{ $t("lss.base_info") }}</div>
            <div class="space-upload">
              <div class="imageUpload">
                <t-upload
                  v-show="organiseType !== 'look'"
                  v-if="files.length < 1"
                  v-model="files"
                  theme="image"
                  accept="image/webp,image/gif,image/bmp,image/png,image/jpg,image/jpeg,image/svg"
                  :abridge-name="[6, 6]"
                  :locale="{
                    triggerUploadText: {
                      image: '',
                    },
                  }"
                  :on-select-change="onSelectChange"
                  @one-file-success="onOneFileSuccess"
                >
                </t-upload>
                <div class="updImgBox">
                  <div v-for="(item, index) in files" :key="item.url" class="imgItemBox">
                    <t-image fit="contain" v-show="item.url" class="imgItem" :src="item.url" @click="handlePreview(item)" />
                    <img
                      v-show="!item.url && organiseType === 'look'"
                      class="imgItem"
                      src="@/assets/svg/clouddisk/temaavatar.svg"
                      @click="handlePreview(item)"
                    />
                    <iconpark-icon
                      v-show="organiseType !== 'look'"
                      class="imgClose"
                      @click="deleImg(index)"
                      name="icon20delet"
                    ></iconpark-icon>
                  </div>
                  <div v-show="files.length < 1 && organiseType === 'look'" class="imgItemBox">
                    <img class="imgItem" src="@/assets/svg/clouddisk/temaavatar.svg" />
                  </div>
                </div>
              </div>
              <t-form-item name="fullname" class="inputForm" style="width: 100%">
                <template #label>
                  <div class="orTag">
                    <div class="orname">
                      <span class="orname-title mr-8">{{ $t("lss.organize_name") }} </span>
                      <iconpark-icon
                        v-show="[1, 3, 4].includes(formData?.auth)"
                        class="imgCertified"
                        :name="onReturnCerIcon(formData)"
                      ></iconpark-icon>

                      <span v-show="formData.auth === 1" class="orname-certified">
                        <img src="@/assets/icon_secure.png" alt="" />
                        {{ $t("lss.certified") }}
                      </span>
                      <span v-show="formData.auth === 0" class="orname-uncertified">
                        {{ $t("lss.uncertifed") }}
                      </span>
                      <span v-show="formData.auth === 2" class="orname-certifing">
                        {{ $t("lss.certified_wait") }}
                      </span>
                      <span v-show="formData.auth === 3" class="orname-certifiedOld">
                        {{ $t("lss.certified_expired") }}
                      </span>
                      <span v-show="formData.auth === 4" class="orname-certifiedOld"> 已驳回 </span>
                    </div>
                    <div class="certified cursor flex-a positiontext" @click="toVerifyWeb">
                      <span>{{ $t("lss.go_certified") }}</span>
                      <iconpark-icon name="iconarrowright" style="width: 20px; height: 20px"></iconpark-icon>
                    </div>
                  </div>
                </template>
                <t-input
                  v-model="formData.fullname"
                  :disabled="formData.auth === 1 || formData.auth === 2 || organiseType === 'look'"
                  :placeholder="$t('lss.one.info_2')"
                  :maxlength="50"
                ></t-input>
              </t-form-item>
            </div>
            <div class="space-item">
              <t-form-item :label="$t('lss.cao.a')" name="title" class="inputForm" style="width: 100%">
                <t-input
                  v-if="organiseType !== 'look' || formData.title"
                  v-model="formData.title"
                  :disabled="organiseType === 'look'"
                  :placeholder="$t('lss.one.info_2')"
                  :maxlength="6"
                ></t-input>
                <t-input
                  v-else-if="organiseType === 'look' && !formData.title"
                  value="--"
                  :disabled="organiseType === 'look'"
                  :placeholder="$t('lss.one.info_2')"
                  :max-length="6"
                ></t-input>
              </t-form-item>
              <t-form-item :label="$t('lss.cao.b')" name="teamId" class="inputForm" style="width: 100%">
                <t-input
                  v-show="organiseType !== 'look' || formData.teamId"
                  v-model="formData.teamId"
                  disabled
                  :placeholder="$t('lss.one.info_2')"
                ></t-input>
              </t-form-item>
            </div>
            <div class="space-item">
              <t-form-item :label="$t('lss.organize_type')" class="inputForm" name="type" style="width: 100%">
                <t-select v-replace-svg  v-model="formData.type" :options="optionsType" disabled placeholder="请选择"> </t-select>
              </t-form-item>

              <t-form-item :label="$t('lss.industry')" name="industry" class="inputForm" style="width: 100%">
                <t-cascader
                  v-if="organiseType !== 'look' || formData.industry"
                  v-model="formData.industry"
                  :options="teamIndustryData"
                  :keys="{ label: 'name', value: 'id' }"
                  :disabled="organiseType === 'look'"
                  @change="onChangeCascader"
                />
                <t-input
                  v-else-if="organiseType === 'look' && !formData.industry"
                  value="--"
                  :disabled="organiseType === 'look'"
                  :placeholder="$t('lss.one.info_2')"
                ></t-input>
              </t-form-item>
            </div>
            <div class="space-item">
              <t-form-item :label="$t('lss.cao.c')" class="inputForm" style="width: 100%" name="size">
                <t-select v-replace-svg
                  v-if="organiseType !== 'look' || formData.size"
                  v-model="formData.size"
                  :keys="{ label: 'name', value: 'id' }"
                  :options="teamSizeData"
                  :disabled="organiseType === 'look'"
                  :placeholder="$t('lss.please_select')"
                >
                </t-select>
                <t-input
                  v-else-if="organiseType === 'look' && !formData.size"
                  value="--"
                  :disabled="organiseType === 'look'"
                  :placeholder="$t('lss.one.info_2')"
                ></t-input>
              </t-form-item>
              <t-form-item :label="$t('lss.cao.d')" name="region" class="inputForm" style="width: 100%">
                <t-select v-replace-svg  v-model="formData.region" disabled :options="REGION_LIST" placeholder="请选择"> </t-select>
              </t-form-item>
            </div>
          </div>
          <div class="space">
            <div class="space-title">{{ $t("lss.one.info_1") }}</div>
            <div class="space-item">
              <t-form-item :label="$t('lss.one.info_3')" name="contact.name" class="inputForm" style="width: 100%">
                <t-input
                  v-if="organiseType !== 'look' || formData.contact.name"
                  v-model="formData.contact.name"
                  :disabled="organiseType === 'look'"
                  :placeholder="$t('lss.one.info_2')"
                  maxlength="50"
                ></t-input>
                <t-input
                  v-else-if="organiseType === 'look' && !formData.contact.name"
                  value="--"
                  maxlength="50"
                  :disabled="organiseType === 'look'"
                  :placeholder="$t('lss.one.info_2')"
                ></t-input>
              </t-form-item>
              <t-form-item class="inputForm" name="contact.telephone" style="width: 100%">
                <template #label>
                  <div class="label">
                    <span :class="{ 'label-title': organiseType !== 'look' }">
                      <span class="text">{{ $t("lss.one.info_4") }}</span>
                    </span>
                    <!-- v-if="organiseType !== 'look'" -->
                    <span class="label-btn"  style="margin-right: -24px" @click="onUpdatePhone">{{
                      $t("lss.one.info_5")
                    }}</span>
                  </div>
                </template>
                <div class="horizationGroup">
                  <select-code-input
                    v-if="organiseType !== 'look' || formData.contact.telCode"
                    v-model:value="formData.contact.telCode"
                    class="phone1"
                    :disabled="true"
                  />
                  <t-input
                    v-else-if="organiseType === 'look' && !formData.contact.telCode"
                    value="--"
                    class="w-88 phone2"
                    :disabled="organiseType === 'look'"
                    :placeholder="$t('lss.one.info_2')"
                  ></t-input>
                  <t-input
                    v-if="organiseType !== 'look' || formData.contact.telephone"
                    v-model="formData.contact.telephone"
                    class="inputT"
                    disabled
                  ></t-input>
                  <t-input
                    v-else-if="organiseType === 'look' && !formData.contact.telephone"
                    value="--"
                    class="inputT"
                    :disabled="organiseType === 'look'"
                    :placeholder="$t('lss.one.info_2')"
                  ></t-input>
                </div>
              </t-form-item>
            </div>
            <div class="space-item">
              <t-form-item :label="$t('lss.one.info_6')" name="contact.email" class="inputForm" style="width: 100%">
                <t-input
                  v-if="organiseType !== 'look' || formData.contact.email"
                  v-model="formData.contact.email"
                  :disabled="organiseType === 'look'"
                  :max-length="100"
                  :placeholder="$t('lss.one.info_2')"
                ></t-input>
                <t-input
                  v-else-if="organiseType === 'look' && !formData.contact.email"
                  value="--"
                  :disabled="organiseType === 'look'"
                  :placeholder="$t('lss.one.info_2')"
                ></t-input>
              </t-form-item>
              <t-form-item :label="$t('lss.cao.e')" class="inputForm" name="contact.phone" style="width: 100%">
                <div class="horizationGroup">
                  <t-input
                    v-if="organiseType !== 'look' || formData.contact.phoneCode"
                    v-model="formData.contact.phoneCode"
                    theme="normal"
                    :maxlength="5"
                    decimal-places="0"
                    class="inputCode"
                    :disabled="organiseType === 'look'"
                    @keyup="onSetPhoneCode"
                  ></t-input>
                  <t-input
                    v-else-if="organiseType === 'look' && !formData.contact.phoneCode"
                    value="--"
                    class="inputCode"
                    :disabled="organiseType === 'look'"
                    :placeholder="$t('lss.one.info_2')"
                  ></t-input>

                  <t-input
                    v-if="organiseType !== 'look' || formData.contact.phone"
                    v-model="formData.contact.phone"
                    :disabled="organiseType === 'look'"
                    :maxlength="10"
                    class="input"
                  ></t-input>
                  <t-input
                    v-else-if="organiseType === 'look' && !formData.contact.phone"
                    value="--"
                    class="input"
                    :disabled="organiseType === 'look'"
                    :placeholder="$t('lss.one.info_2')"
                  ></t-input>
                </div>
              </t-form-item>
            </div>
            <div class="space-item">
              <t-form-item :label="$t('lss.one.info_7')" name="contact.address" class="inputForm" style="width: 100%">
                <div class="verticalGroup">
                  <span v-if="organiseType !== 'look' || formData.contact.address_code">
                    <select-area-input
                      v-model:value="formData.contact.address_code"
                      :disabled="organiseType === 'look'"
                      :placeholder="$t('lss.one.info_8')"
                      :is-show-control="false"
                    />
                  </span>
                  <t-input
                    v-else-if="organiseType === 'look' && !formData.contact.address_code"
                    value="--"
                    class="input"
                    :disabled="organiseType === 'look'"
                    :placeholder="$t('lss.one.info_2')"
                  ></t-input>

                  <t-input
                    v-if="organiseType !== 'look' || formData.contact.address"
                    v-model="formData.contact.address"
                    :disabled="organiseType === 'look'"
                    :placeholder="$t('lss.one.info_9')"
                    class="input"
                  ></t-input>
                  <t-input
                    v-else-if="organiseType === 'look' && !formData.contact.address"
                    value="--"
                    class="input"
                    :disabled="organiseType === 'look'"
                    :placeholder="$t('lss.one.info_2')"
                  ></t-input>
                </div>
              </t-form-item>
            </div>
          </div>
        </t-form>
      </t-space>
    </div>
  </div>

  <orgAuth v-if="orgAuthVisible" v-model:visible="orgAuthVisible" :showType="orgAuthShowType" :region="orgAuthDetail.region" :teamId="orgAuthDetail.teamId" :orgType="orgAuthDetail.orgType" @success="onSuccess"/>
  <update-phone-modal ref="updatePhoneRef" @reload="initData" />
</template>
<script setup name="workBenchEnterprise">
import { ref, onMounted, computed, watch, nextTick, onUnmounted, reactive } from "vue";
import { useI18n } from "vue-i18n";
import { useRoute } from "vue-router";
import { AddIcon } from "tdesign-icons-vue-next";
import { MessagePlugin } from "tdesign-vue-next";
import { getStsToken } from "@/api/cloud";
import { getResponseResult } from "@renderer/utils/myUtils";
import { marketClassifytreezzh } from "@renderer/api/business/index";
import ModuleSeeting from "../components/ModuleSeeting.vue";
import UpdatePhoneModal from "./UpdatePhoneModal.vue";
import SelectCodeInput from "../components/SelectCodeInput.vue";
import { onMountedOrActivated } from "@/hooks/onMountedOrActivated";
import { getTeamInfoAxios, patchTeamInfoAxios, getTeamSizeAxios } from "@renderer/api/groupInfo/index";
import { jumpWeb } from "@renderer/views/contacts/utils";
import SelectAreaInput from "@renderer/views/engineer/components/SelectAreaInput.vue";
import orgAuth from "@renderer/components/orgAuth/orgAuth.vue";
import { orgAuthStatusMap, orgTypeMap } from '@renderer/components/orgAuth/utils';
import {
  getTeams
} from '@renderer/api/contacts/api/organize';
import LynkerSDK from '@renderer/_jssdk';

const { ipcRenderer, shell } = LynkerSDK;

const route = useRoute();
const orgAuthVisible = ref(false);
const orgAuthDetail = ref(null);

const orgAuthShowType = computed(() => orgAuthDetail.value?.auth === 0 ? 'edit' : 'detail');
const updatePhoneRef = ref(null);
const props = defineProps({
  activationGroupItem: {
    type: Object,
    default: () => {},
  },
});
const files = ref([]);
const { t } = useI18n();
const organiseType = ref("look"); // look默认查看状态， 'update'编辑状态
const REGION_LIST = [
  { label: t("lss.cao.g"), value: "CN" },
  { label: t("lss.cao.h"), value: "MO" },
  { label: t("lss.cao.i"), value: "HK" },
];
const optionsType = [
  { label: t("lss.one.info_10"), value: 1 },
  { label: t("lss.one.info_11"), value: 2 },
  { label: t("lss.one.info_12"), value: 3 },
  { label: t("lss.one.info_13"), value: 0 },
  { label: t("lss.one.info_14"), value: 4 },
];
const FORM_RULES = ref(null);

// 组织信息-获取组织行业列表
const teamIndustryData = ref([]);
const formData = reactive({
  // organise: '企业',

  /**
   * 认证，0：未认证，1：已认证，2：认证审核中，3：认证已过期
   */
  auth: undefined,
  /**
   * 联系信息
   */
  contact: {
    /**
     * 详细地址
     */
    address: undefined,
    /**
     * 市
     */
    city: undefined,
    /**
     * 国家
     */
    country: undefined,
    /**
     * 区
     */
    district: undefined,
    /**
     * 邮箱
     */
    email: undefined,
    /**
     * 姓名
     */
    name: undefined,
    /**
     * 座机
     */
    phone: undefined,
    /**
     * 座机区号
     */
    phoneCode: undefined,
    /**
     * 省
     */
    province: undefined,
    /**
     * 手机区号
     */
    telCode: undefined,
    /**
     * 手机号
     */
    telephone: undefined,

    address_code: undefined,
  },
  /**
   * 团队全称
   */
  fullname: undefined,
  /**
   * 组织自增ID
   */
  // idTeam: number;
  /**
   * 行业
   */
  industry: undefined,
  /**
   * 行业文本
   */
  industry_text: undefined,
  /**
   * 标志图片资源
   */
  logo: "",
  /**
   * 所在地区
   */
  region: undefined,
  /**
   * 驳回理由
   */
  remark: undefined,
  /**
   * 组织规模
   */
  size: undefined,
  /**
   * 认证状态，0：待审核，1：已完成，2：已驳回
   */
  status: undefined,
  /**
   * 组织标识
   */
  teamId: "",
  /**
   * 启用主域名对应的团队邮箱，免认证加入（直接批准）
   */
  teamMailApproved: undefined,
  /**
   * 团队的主域名
   */
  teamMailDomain: undefined,
  /**
   * 团队简称
   */
  title: undefined,
  /**
   * 类型 0其他 1企业
   */
  type: undefined,
});

const initFormRule = () => {
  FORM_RULES.value = {
    fullname: [{ required: organiseType.value !== "look", message: t("lss.one.info_2") }], // 组织名称
    title: [{ required: organiseType.value !== "look", message: t("lss.one.info_2") }], // 组织简介
    type: [{ required: organiseType.value !== "look", message: t("lss.one.info_2") }], // 组织类型
    teamId: [{ required: false, message: t("lss.one.info_2") }], // teamId
    industry: [{ required: organiseType.value !== "look", message: t("lss.please_select") }], // 行业
    size: [{ required: organiseType.value !== "look", message: t("lss.please_select") }], // 组织规模
    region: [{ required: organiseType.value !== "look", message: t("lss.one.info_2") }], // 数据驻留地
    "contact.name": [{ required: organiseType.value !== "look", message: t("lss.one.info_2") }], // 姓名
    "contact.phone": [{ pattern: /^\d+$/, message: "只允许输入数字" }],
    // 'contact.telephone': [{ required: organiseType.value !== 'look', message: '请输入' },{ pattern: /^\d+$/, message: '只允许输入数字' }],
    "contact.email": [
      { pattern: /^[A-Za-z0-9\u4e00-\u9fa5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/, message: t("lss.cao.f") },
    ],
    // phone,
  };
};
const handlePreview = (item) => {

  console.log(item, "itemmmmmmmmm");
  ipcRenderer.invoke("view-img", JSON.stringify(item));
};

const onGetTeamIndustry = () => {
  console.log("onGetTeamIndustry");
  let res = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      // res = await getTeamIndustryAxios();
      console.log(props.activationGroupItem, "props.activationGroupItem?.regionprops.activationGroupItem?.region");
      console.log(
        props.activationGroupItem?.teamRegion,
        "props.activationGroupItem?.regionprops.activationGroupItem?.region",
      );
      res = await marketClassifytreezzh(props.activationGroupItem?.teamRegion);
      console.log(res, "11sada");

      res = getResponseResult(res);
      console.log(res, "sada");
      if (!res) {
        reject();
        return;
      }

      teamIndustryData.value = res.data;
      resolve(res.data);
    } catch (error) {
      console.log(error, "errorrrrrrrrrrr");
      const errMsg = error.response.data.message;
      if (errMsg) MessagePlugin.error(errMsg);
      reject(errMsg);
    }
  });
};


const getOrgAuthDetail = async () => {

  return new Promise(async (resolve, reject) => {
    try {
      const res = await getTeams({ teamId: props.activationGroupItem.teamId  });
      console.log(res);
      if (res?.data?.code === 0) {
        orgAuthDetail.value = { ...res?.data?.data, orgType: orgTypeMap.get(res?.data?.data?.type)?.orgType };
        resolve(res?.data?.data);
      }
    } catch (error) {
      reject(error)
    }

  });

};


const onUpdate = () => {
  organiseType.value = "update";
  initFormRule();
};
const deleImg = (index) => {
  files.value.splice(index, 1);
  formData.logo = "";
};
onMountedOrActivated(() => {
  if (props.activationGroupItem?.teamId) {
    initData();

  }
  // toVerifyWeb
  if(tabStore.isOpenCertifyModal){
    toVerifyWeb();
    tabStore.setOpenCertifyModal(false);
  }

});
const initData = async () => {
  if (props.activationGroupItem?.teamId) {
    const res = await Promise.all([onGetTeamIndustry(), onGetTeamSize()]);
    console.log(res, "啊实打实大");

    await onGetTeamInfo();
  }
};
const onSuccess = (params, res)=> {
  console.log(params, res)
  onGetTeamInfo();
}
const setDefaultValue = (val) => {
  return val || "";
};
const organizeInfo = ref(null);
watch(
  () => props.activationGroupItem.teamId,
  (newValue) => {
    initData();
  },
);
const onSetFormData = () => {
  if (organizeInfo.value) {
    // 0：未认证，1：已认证，2：认证审核中，3：认证已过期
    formData.auth = organizeInfo.value.auth;
    formData.fullname = setDefaultValue(organizeInfo.value.fullname);
    formData.industry = setDefaultValue(organizeInfo.value.industry);
    formData.industry_text = setDefaultValue(organizeInfo.value.industry_text);
    formData.logo = setDefaultValue(organizeInfo.value.logo);
    // 需要将图片存入files
    files.value = formData.logo
      ? [
          {
            url: formData.logo,
            status: "success",
            response: { url: formData.logo },
          },
        ]
      : [];

    formData.region = organizeInfo.value.region;
    formData.remark = setDefaultValue(organizeInfo.value.remark);
    formData.size = setDefaultValue(organizeInfo.value.size);
    formData.status = setDefaultValue(organizeInfo.value.status);
    formData.teamId = setDefaultValue(organizeInfo.value.teamId);
    formData.teamMailApproved = setDefaultValue(organizeInfo.value.teamMailApproved);
    formData.teamMailDomain = setDefaultValue(organizeInfo.value.teamMailDomain);
    formData.title = setDefaultValue(organizeInfo.value.title);
    formData.type = Number(setDefaultValue(organizeInfo.value.type));
    formData.contact.address = setDefaultValue(organizeInfo.value.contact.address);
    formData.contact.city = setDefaultValue(organizeInfo.value.contact.city);
    formData.contact.country = setDefaultValue(organizeInfo.value.contact.country);
    formData.contact.district = setDefaultValue(organizeInfo.value.contact.district);
    formData.contact.email = setDefaultValue(organizeInfo.value.contact.email);
    formData.contact.name = setDefaultValue(organizeInfo.value.contact.name);
    formData.contact.phone = setDefaultValue(organizeInfo.value.contact.phone);
    formData.contact.phoneCode = setDefaultValue(organizeInfo.value.contact.phoneCode);
    formData.contact.province = setDefaultValue(organizeInfo.value.contact.province);
    formData.contact.telCode = setDefaultValue(organizeInfo.value.contact.telCode);
    formData.contact.telephone = setDefaultValue(organizeInfo.value.contact.telephone);
    formData.contact.address_code = setDefaultValue(organizeInfo.value.contact.address_code);
  }
};
// 获取组织信息
const onGetTeamInfo = () => {
  let res = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      res = await getTeamInfoAxios(props.activationGroupItem.teamId);
      console.log(res, "啊实打实大萨斯的");
      res = getResponseResult(res);
      if (!res) return;
      organizeInfo.value = res.data;
      onSetFormData();
      setTimeout(() => {
        form.value?.clearValidate?.();
      });
      resolve(res.data);
    } catch (error) {
      const errMsg = error.response.data.message;
      if (errMsg) MessagePlugin.error(errMsg);
      reject(errMsg);
    }
  });

  // console.log(res);
};
const onCancel = () => {
  // 取消
  organiseType.value = "look";
  initFormRule();
  return initData();
};
const onReturnCerIcon = (item) => {
  // 0：其他 1：企业，2：商协会，3：个体户
  const iconNames = ["iconother", "iconenterprise", "iconbusiness", "iconindividual", "icongov"];
  // 0：未认证，1：已认证，2：认证审核中，3：认证已过期
  let suffix = "default";
  if (item?.auth === 3) {
    suffix = "disabled";
  }
  console.log(item, "imgCertifiedimgCertified");
  console.log(iconNames[item?.type], "iconNames[item?.type]");
  return iconNames[item?.type];
};
const teamSizeData = ref([]);
const form = ref(null);

const onGetTeamSize = () => {
  let res = null;

  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      res = await getTeamSizeAxios(props.activationGroupItem.teamId);
      res = getResponseResult(res);
      if (!res) return;
      teamSizeData.value = res.data;
      resolve(res.data);
    } catch (error) {
      const errMsg = error.response.data.message;
      if (errMsg) MessagePlugin.error(errMsg);
      reject(errMsg);
    }
  });
};
const emits = defineEmits(["getGroupListApi"]);

const onChangeCascader = (val, context) => {
  console.log(val, context);
  formData.industry_text = context.node.label;
};
const requestFailMethod = (file) => {
  console.log(file);
  return new Promise((resolve) => {
    // resolve 参数为关键代码
    resolve({ status: "fail", error: "上传失败，请检查文件是否符合规范" });
  });
};
const onUpdateTeamInfo = async () => {
  let res = null;
  const params = {
    ...formData,
  };

  // address_code 拆分出国家省市区
  if (params.contact.address_code && params.contact.address_code.select) {
    params.contact.address_code.select.forEach((item, itemIndex) => {
      if (itemIndex === 0) {
        params.contact.country = item.name;
      } else if (itemIndex === 1) {
        params.contact.province = item.name;
      } else if (itemIndex === 2) {
        params.contact.city = item.name;
      } else if (itemIndex === 3) {
        params.contact.district = item.name;
      }
    });
  }

  try {
    res = await patchTeamInfoAxios(params,{teamId: props.activationGroupItem.teamId});
    // form.value.fullname
    // form.value.setValidateMessage({ fullname: [{ type: 'error', message: '你输入的信息包含敏感内容，请修改后重试' }] });
    // 塞入合规
    res = getResponseResult(res);
    if (!res) return;
    MessagePlugin.success("保存成功");
    // 更新下左上角的数据start

    emits("getGroupListApi", props.activationGroupItem.teamId);
    // end

    initData();
    form.value.clearValidate();
    organiseType.value = "look";
  } catch (error) {
    const errMessage = error instanceof Error ? error.message : error;

    MessagePlugin.error(errMessage);
  }
};

const onSave = () => {
  form.value.validate({ showErrorMessage: true }).then(async (validateResult) => {
    console.log(validateResult);
    if (validateResult && Object.keys(validateResult).length) {
      console.log(formData);
    } else {
      // 组装提交的数据
      onUpdateTeamInfo();
    }
  });
};

const client = ref(null);
const paddingUdpList = ref([]);
const formatDate = (timeStamp) => {
  const date = new Date(timeStamp); // 创建Date对象
  const year = date.getFullYear(); // 获取年份
  const month = date.getMonth() + 1; // 获取月份，记得+1
  const day = date.getDate(); // 获取日期
  return `${year}/${month}/${day}`; // 返回格式化后的日期字符串
};
const onSelectChange = async (file, val) => {
  console.log(file, "onSelectChange");
  console.log(val, "onSelectChangevalvalval");
  const isSize = file[0].size > 1 * 1024 * 1024;
  const isfileType =
    file[0].type === "image/jpeg" ||
    file[0].type === "image/jpg" ||
    file[0].type === "image/png" ||
    file[0].type === "image/bmp" ||
    file[0].type === "image/gif" ||
    file[0].type === "image/webp" ||
    file[0].type === "image/svg" ||
    file[0].type === "image/svg+xml";
  if (!isfileType) {
    MessagePlugin.error("请上传jpeg，jpg，png，bmp，gif，webp，svg格式的文件 ");
    return;
  }
  if (isSize) {
    MessagePlugin.error("文件过大，请压缩到1M以内");
    return;
  }

  paddingUdpList.value = val.currentSelectedFiles;

  getStsToken().then((res) => {
    client.value = new OSS({
      region: "oss-cn-shenzhen",
      accessKeyId: res.data.data.AccessKeyId,
      accessKeySecret: res.data.data.AccessKeySecret,
      stsToken: res.data.data.SecurityToken,
      refreshSTSTokenInterval: 300000,
      bucket: "kuaiyouyi",
      secure: true,
    });
    paddingUdpList.value.map((e) => {
      const fileName = e.name;
      const types = fileName.substring(fileName.lastIndexOf(".") + 1);
      const newName = `teamInfo/${formatDate(new Date().getTime())}/${fileName + new Date().getTime()}.${types}`;
      client.value
        .multipartUpload(newName, e.raw)
        .then((res) => {
          console.log(res, "resssssssss");
          let cleanUrl = res.res.requestUrls[0].split('?')[0];
          files.value = [
            {
              url:cleanUrl,
            },
          ];
          formData.logo =cleanUrl;
        })
        .catch((err) => {
          console.log(err, "errerrerr");
        });
    });
  });
};
const toVerifyWeb = () => {
  // jumpWeb("/#/setting/setting-info/setting-info-detail", { teamId: props.activationGroupItem.teamId });
  getOrgAuthDetail().then(()=> {
    orgAuthVisible.value = true;
  })
};
const onUpdatePhone = () => {
  updatePhoneRef.value.onOpen({ phone: formData.contact.telephone, code: formData.contact.telCode });
};
const beforeFileUpload = (file, fileList) => {
  // const isSize = file.size > 1 * 1024 * 1024;

  // const isfileType =
  //   file.type === "image/jpeg" ||
  //   file.type === "image/jpg" ||
  //   file.type === "image/png" ||
  //   file.type === "image/bmp" ||
  //   file.type === "image/gif" ||
  //   file.type === "image/webp" ||
  //   file.type === "image/svg" ||
  //   file.type === "image/svg+xml";
  // // file.type === 'application/pdf'

  // if (!isfileType) {
  //   MessagePlugin.error("请上传jpeg，jpg，png，bmp，gif，webp，svg格式的文件 ");
  //   return false;
  // }
  // if (isSize) {
  //   MessagePlugin.error("文件过大，请压缩到1M以内");
  //   // eslint-disable-next-line prefer-promise-reject-errors
  //   return false;
  // } else {
  //   return true;
  // }
  return false;
};
const openTeamCertification = computed(() => {
  return route?.query?.openTeamCertification || '';
})
// console.error(openTeamCertification, "route.queryroute.queryroute.query");
const openTeamCertificationFn = async () => {
  await onGetTeamInfo();
  toVerifyWeb();
}
watch(openTeamCertification, () => {
  if (openTeamCertification.value) {
    openTeamCertificationFn();
  }
})

onMounted(() => {
  if (openTeamCertification.value) {
    console.error('openTeamCertification', openTeamCertification.value);
    openTeamCertificationFn();
  }
})

</script>

<style lang="less" scoped>
.team-info-box {
  padding: 16px 24px;
  overflow-y: auto;
  width: 100%;
  .footer {
    width: 100%;
    display: flex;
    align-items: center;
    padding: 0 0 16px;
    flex: none;
  }
}
.updImgBox {
  .imgItemBox {
    .imgItem {
      width: 88px;
      height: 88px;
      border-radius: 50%;
    }
  }
}
.space-title {
  margin-left: 11px;
  color: var(--text-kyy_color_text_1, #1a2139);
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 24px;
  position: relative;
  margin-bottom: 24px;
}

.space-title:after {
  content: "";
  width: 3px;
  height: 16px;
  border-radius: 8px;
  background: var(--brand-kyy_color_brand_default, #4d5eff);
  position: absolute;
  left: -8px;
  top: 4px;
}
.imgCertified {
  color: #fff;
  width: 24px;
  height: 24px;
  font-size: 24px;
}
.orname {
  display: flex;
  align-items: center;
  &-certified {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 4px;
    background: #eeffe8;
    border-radius: 4px;
    font-size: 12px;
    font-family: Microsoft YaHei, Microsoft YaHei-Regular;
    font-weight: 400;
    color: #499d60;
    line-height: 20px;
    text-align: center;
    margin-left: 8px;
    .iconpark-icon {
      color: #499d60;
      width: 16px;
      height: 16px;
    }
  }
  &-uncertified {
    padding: 0 7px;

    border-radius: 4px;
    font-size: 12px;
    font-family: Microsoft YaHei, Microsoft YaHei-Regular;
    font-weight: 400;
    color: #717376;
    line-height: 20px;
    border: 1px solid #eceff5;
    text-align: center;
  }
  &-certifing {
    background: #f0f8ff;
    border-radius: 4px;
    font-size: 12px;
    font-family: Microsoft YaHei, Microsoft YaHei-Regular;
    font-weight: 400;
    padding: 0 7px;
    color: #4d5eff;
    line-height: 20px;
    text-align: center;
  }
  &-certifiedOld {
    background: #ffeee8;
    border-radius: 4px;
    font-size: 12px;
    font-family: Microsoft YaHei, Microsoft YaHei-Regular;
    font-weight: 400;
    padding: 0 7px;
    color: #da2d19;
    line-height: 20px;
    text-align: center;
  }
}
.orname-title {
  color: var(--text-kyy_color_text_3, #828da5);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}
.minw83 {
  min-width: 83px;
}
.orTag {
  display: inline-flex;
  justify-content: space-between;
  align-items: center;
  /* position: relative; */
  width: 100%;
  margin-bottom: 4px;
  .certified {
    font-size: 14px;
    font-family: Microsoft YaHei, Microsoft YaHei-Regular;
    font-weight: 400;

    color: #4d5eff;
    line-height: 22px;
    margin-right: -24px;
  }
}

.container {
  background-color: #ffffff;
  border-radius: 8px;
  padding-bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  .space {
    width: 100%;
    &-upload {
      display: flex;
      margin-bottom: 24px;
      position: relative;
      .inputForm {
        margin-left: 16px;
      }
    }
    &-item {
      display: flex;
      justify-content: space-between;
      gap: 32px;
    }
  }
  .bok {
    flex: 1;
    overflow: auto;
    :deep(.t-form) {
      overflow-y: overlay;
    }
  }
}
:deep(.t-is-disabled) {
  border: 1px solid var(--input-kyy_color_input_border_readonly, #eceff5) !important;
  background: var(--input-kyy_color_input_bg_readonly, #fff) !important;
  color: #acb3c0 !important;
}
:deep(.t-is-disabled .t-input__inner){
        color: var(--input-kyy_color_input_text_disabled, #ACB3C0) !important;
      }
      :deep(.t-is-disabled .t-input__suffix-icon){
        /* color: var(--input-kyy_color_input_text_disabled, #ACB3C0) !important; */
        display: none ;
      }

.positiontext {
  display: flex;
  align-items: center;
  position: absolute;
  right: 25px;
  top: 0;
}
:deep(.t-form__label) {
  line-height: 24px;
  margin-bottom: 8px;
}
:deep(.t-form__label--top) {
  min-height: 10px;
}
.horizationGroup {
  display: flex;
  align-items: center;
  position: relative;
  width: 100%;
  .select {
    width: 88px;
    line-height: 12px;
    margin-right: 8px;
  }
  .inputCode {
    width: 88px;
  }
}
.label {
  display: flex;
  justify-content: space-between;
  width: 100%;
  &-title {
    &::before {
      display: inline-block;
      margin-right: var(--td-comp-margin-xs);
      color: var(--td-error-color);
      line-height: var(--td-line-height-body-medium);
      content: "*";
    }
  }
  &-btn {
    font-size: 14px;
    font-family: Microsoft YaHei, Microsoft YaHei-Regular;
    font-weight: 400;
    color: #4d5eff;
    cursor: pointer;
    user-select: none;
  }
}
:deep(.t-upload__card-container) {
  width: 88px;
  height: 88px;
}
.verticalGroup {
  display: flex;
  flex-direction: column;
  position: relative;
  width: 100%;
  .input {
    margin-top: 8px;
  }
}
</style>
