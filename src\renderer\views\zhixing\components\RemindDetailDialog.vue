<template>
  <div>
    <!-- 无后续提醒 -->
    <div v-if="props.isDialog">
      <t-drawer v-model:visible="zhixingStore.remindDetailDialogData.visible" showInAttachedElement destroyOnClose
        :showOverlay="zhixingStore.remindDetailDialogData.showOverlay" :header="false" :footer="false"
        :size="isDetail ? '376px' : '376px'" :zIndex="1498">
        <div class="drawerBox">
          <!-- 详情(场景、知行单独模块) -->
          <template v-if="isDetail">
            <div class="drawerHeader">
              <div>
                <t-button v-if="isMentionUser && userDone" variant="outline" theme="default" disabled>{{
                  t('zx.remind.finished') }}</t-button>
                <t-button v-if="isMentionUser && !userDone" variant="outline" theme="primary" @click="finishRemind">{{
                  t('zx.remind.finish') }}</t-button>
                <t-button v-if="!isMentionUser && !isOwner" variant="outline" theme="default" @click="saveToMy">{{
                  t('zx.remind.saveMyRemind') }}</t-button>
              </div>
              <div class="act-icon-group">
                <iconpark-icon name="iconedit" class="icon"
                v-if="isOwner && formData.noticeTyp !== 'ONCE' && remindDetailData.mentionUsers?.find(v=>v.openid === getOpenid())?.finishAt === '0'"
                  @click="onRepeatEdit"></iconpark-icon>
                <iconpark-icon name="iconedit" class="icon"
                v-else-if="isOwner && !userDone && !formData.mentionUsers.some(v=>v.finishAt !== '0')"
                  @click="isDetail = false"></iconpark-icon>
                <!-- <iconpark-icon name="iconedit" class="icon" v-if="isOwner" @click="saveToMy"></iconpark-icon> -->
                <iconpark-icon name="iconshare2" class="icon" v-if="isOwner || isMentionUser"
                  @click="shareClick"></iconpark-icon>
                <iconpark-icon name="icondelete" class="icon" @click="deleteClick"></iconpark-icon>
                <iconpark-icon name="iconerror" class="icon" @click="close"></iconpark-icon>
              </div>
            </div>
            <div class="drawerFrom" v-if="remindDetailData && remindDetailData?.fromName && getOpenid() === remindDetailData?.owner">
              <img src="@renderer/assets/img/icon_info.svg"
                style="width: 20px;height: 20px;margin-right: 8px;vertical-align: middle;">
              来自{{ remindDetailData?.typ === 'GROUP' ? '群聊' : '单聊' }}： {{ remindDetailData?.fromName }}
              <span class="drawerFrom_name" @click="goToIm">点击查看</span>
            </div>
            <div class="drawerContent drawerContent-edit">
              <t-form ref="formRef" class="formDisabledRef" :rules="formRules" :data="formData" labelWidth="80"
                :requiredMark="false">
                <t-form-item label="" name="title" class="form-item title-item">
                  <t-textarea readonly v-model="formData.title" placeholder="" :maxlength="255" :autosize="true" />
                </t-form-item>
                <t-form-item label="" name="desc" class="form-item desc-item" v-if="formData.desc">
                  <t-textarea readonly v-model="formData.desc" placeholder="描述" :maxlength="255"
                    style="border: 0;border-radius: 4px;resize:none;" autosize/>
                </t-form-item>
                <!-- 开始 -->
                <t-form-item
                class="form-item"
                v-if="formData?.noticeMatters && formData?.noticeMatters.length">
                  <div style="width:100%;">
                    <div class="form-item-title" style="padding-bottom:0px;">
                      <!-- <img src="@renderer/assets/zhixing/icon-item-big.svg" alt="" style="width:15px;height: 15px;margin-right: 5px;"> -->
                      <iconpark-icon name="iconlist"
                        style="font-size:20px;margin-right: 3px;color:rgba(130, 141, 165, 1)"></iconpark-icon>
                      提醒事项
                    </div>
                    <div class="remind-item" v-for="(item, index) in formData.noticeMatters" :key="item.name + index">
                      <div class="f-align">
                        <t-checkbox v-model="item.finish" @change="(v) => updateMatters(item)"></t-checkbox>
                        <span>{{ item.name }}</span>
                      </div>
                      <div class="split" v-if="item.finishUser"></div>
                      <div class="f-align" v-if="item.finishUser">
                        <avatar class="avatar-icon" :imageUrl="item.finishUser.avatar" :userName="item.finishUser.name"
                          avatarSize="20px" />
                        <span class="user-name">{{ item.finishUser.name }}</span>
                        <span class="finish-time">完成于{{ moment(Number(item.finishUser.finishAt || item.finishUser.finish_at)).format('YYYY-MM-DD HH:mm') }}</span>
                      </div>
                    </div>
                  </div>
                </t-form-item>
                <t-form-item class="form-item">
                  <div>
                    <div class="form-item-title" style="padding-bottom:12px;">
                      <!-- <img src="@renderer/assets/zhixing/icon-item-big.svg" alt="" style="width:14px;height: 13px;margin-right: 4px;"> -->
                      <iconpark-icon name="iconpeople"
                        style="margin-right: 3px;font-size:20px;color:rgba(130, 141, 165, 1)"></iconpark-icon>
                      提醒到人
                    </div>
                    <div style="margin-left: 16px;display: flex;flex-wrap: wrap;">
                      <div class="user-info" v-for="item in formData.mentionUsers" :key="item.openid">
                        <avatar class="avatar-icon" :imageUrl="item.avatar" :userName="item.name" avatarSize="44px" />
                        <div class="user-name">{{ item.name }}</div>
                        <img v-if="item.finishAt !== '0'" src="@renderer/assets/zhixing/icon-suc-small.png" alt="" class="icon">
                      </div>
                    </div>
                  </div>
                </t-form-item>
                <!-- 结束 -->
                <t-form-item name="noticeTyp" class="form-item">
                  <div class="form-item-title">
                    <iconpark-icon name="iconsubmit"
                      style="font-size:20px;margin-right: 3px;color:rgba(130, 141, 165, 1)"></iconpark-icon>
                    {{ t('zx.remind.frequency') }}
                  </div>
                  <div style="display: flex;margin-left:5px;padding-top:16px;">

                    <div style="padding:0 0 0 11px;">{{ checkFrequency(formData.noticeTyp) }}</div>
                  </div>

                </t-form-item>
                <t-form-item name="time" class="form-item">
                  <div class="form-item-time-box">
                    <div class="form-item-title" style="padding-top:16px;">
                      <iconpark-icon name="icondate"
                        style="font-size:20px;margin-right: 3px;color:rgba(130, 141, 165, 1)"></iconpark-icon>
                      {{ t('zx.remind.timeDate') }}
                    </div>
                    <div class="form-item-time-box-time-show"
                      :style="{ paddingTop: showDatePicker('ONCE') ? '17px' : '15px' }">
                      <span style="margin-left:15px;">{{ formData.dateTime }}</span>
                    </div>
                  </div>

                </t-form-item>
                <!-- 重复提醒 -->
                <t-form-item class="form-item" v-if="formData.content">
                  <div class="form-item-title">
                    <!-- <img src="@renderer/assets/zhixing/icon-item-big.svg" alt="" style="width:14px;height: 13px;margin-right: 4px;"> -->
                    <iconpark-icon name="iconlink"
                      style="font-size:20px;margin-right:3px;color:rgba(130, 141, 165, 1)"></iconpark-icon>
                    {{ t('zx.remind.associationBusiness') }}
                  </div>
                </t-form-item>
              </t-form>
              <div class="card-box" v-if="formData.content" @click="openCard">
                <img class="avatar" :src="formData.content?.icon" alt="">
                <div class="text">{{ formData.content?.title }}</div>
              </div>
            </div>
            <!-- <div class="drawerFooter">
              <t-button block variant="outline" theme="primary" @click="saveToMy">{{ t('zx.remind.saveMyRemind') }}</t-button>
            </div> -->
          </template>
          <!-- 详情编辑 -->
          <template v-else>
            <div class="edit-container">
              <div class="drawerHeader">
                <div class="title">
                  <!-- <t-button disabled variant="outline" theme="primary" v-if="zhixingStore.remindDetailDialogData.props?.done">{{ t('zx.remind.finished') }}</t-button>
                <template v-else>
                  <t-button variant="outline" theme="primary" @click="finishClick">{{ t('zx.remind.finish') }}</t-button>
                  <t-button variant="outline" theme="default" v-if="isRepeatRemind" @click="editClick">{{ t('zx.remind.editRemind') }}</t-button>
                </template> -->
                  编辑提醒详情
                </div>
                <div @click="isDetail = true">
                  <img style="height:16px;cursor: pointer;" src="@/assets/im/im_close.png">
                </div>
              </div>
              <div class="drawerContent" v-if="showFormRef">
                <t-form ref="formRef" :rules="formRules" :data="formData" labelWidth="0" resetType="initial">
                  <t-form-item label="" name="title" requiredMark class="form-item">
                    <div class="form-item-label-box">
                      <div style="color: red">*</div>
                      <div class="form-item-label">{{ t('zx.remind.title') }}</div>
                    </div>
                    <t-input v-model="formData.title" :placeholder="t('zx.remind.inputTip')" :maxlength="255" clearable />
                  </t-form-item>
                  <t-form-item :label="t('zx.remind.desc')" name="desc" class="form-item">
                    <div class="form-item-label-box">
                      <div class="form-item-label">{{ t('zx.remind.desc') }}</div>
                    </div>
                    <t-textarea v-model="formData.desc" :placeholder="t('zx.remind.inputTip')" :maxlength="255"
                      :autosize="{ minRows: 3, maxRows: 5 }" />
                  </t-form-item>
                  <t-form-item class="form-item">
                    <div class="f-between" style="width: 100%">
                      <div class="form-item-label">提醒事项</div>
                      <img class="add-addpend" src="@renderer/assets/zhixing/icon_addpend.svg" alt=""
                        @click="addNoticeMatter">
                    </div>
                    <draggable :list="formData.noticeMatters" animation="300" itemKey="index" @end="onEnd"
                      style="width: 100%">
                      <template #item="{ element, index }">
                        <div class="f-between" style="margin-top: 12px">
                          <iconpark-icon name="icondrag"></iconpark-icon>
                          <t-input style="margin: 0 12px;" v-model="element.name" :maxlength="100"></t-input>
                          <img class="add-addpend" src="@renderer/assets/zhixing/icon_delete.svg" alt=""
                            @click="removeNoticeMatter(index)">
                        </div>
                      </template>
                    </draggable>
                  </t-form-item>
                  <t-form-item label="" name="mentionUsers" class="form-item" requiredMark>
                    <div class="f-between" style="width: 100%">
                      <div class="form-item-label">
                        <span style="color: red">*</span>提醒到人
                      </div>
                    </div>
                    <div class="user-container-edit">
                      <div class="user-container-left" :class="{'!gap-8': formData.mentionUsers.length > 1}">
                        <div class="container-left-list"
                        v-for="item in formData.mentionUsers" :key="item.openid"
                        >
                          <avatar class="avatar-icon" :imageUrl="item.avatar" :userName="item.name" avatarSize="24px" />
                          <div class="container-left-list-name">{{ item.name }}</div>
                          <iconpark-icon name="iconerror" class="icon-delete" @click="removeUser(item)"
                          v-if="item.cardId !== getOpenid()"
                          ></iconpark-icon>
                          <!-- <img src="@renderer/assets/zhixing/icon-close.svg" alt="" class="icon"
                          @click="removeUser(item)"> -->
                        </div>
                        <div v-show="formData.mentionUsers.length > 1" class="user-popup-container w-full relative"></div>
                        <div class="container-left-add">
                          <t-popup
                              :attach="formData.mentionUsers.length > 1 ? '.user-popup-container' : '.user-container'"
                              overlay-class-name="user-container-popup-name-edit"
                              content="这是一个弹出框" trigger="click">
                              <span style="width: 100%;">添加提醒到人</span>
                              <template #content>
                                <div class="user-container-popup">
                                  <div class="user-container-popup-input" v-if="conversation !== 1">
                                    <t-input
                                      @change="handleSearch"
                                      :placeholder="t('account.search')">
                                      <template #prefixIcon>
                                        <iconpark-icon name="iconsearch" style="font-size: 20px;"></iconpark-icon>
                                      </template>
                                    </t-input>
                                  </div>
                                  <div class="user-container-popup-content">
                                    <div class="user-container-popup-content-name" v-if="sessionId">
                                      {{ conversation === 3 ? '群聊成员' : '单聊成员'}}
                                    </div>
                                    <div class="user-container-popup-content-name" v-else-if="!conversation || conversation === 1">最近联系人</div>
                                    <div class="user-container-popup-content-box" v-if="pickListData.length">
                                      <div class="user-container-popup-box-list"
                                      v-for="(item, index) in pickListData" :key="index"
                                      >
                                        <div class="user-container-popup-box-list-left">
                                          <t-checkbox :checked="item.done" v-model="item.done" @change="onChangePick"></t-checkbox>
                                        </div>
                                        <div class="user-container-popup-box-list-right">
                                          <avatar class="avatar-icon" :imageUrl="item.avatar" :userName="item.name" avatarSize="32px" />
                                          <!-- <img class="icon-box-list-right"
                                            src="https://img.kuaiyouyi.com/avatar/f03648ed61bb6572be67bd46f8832acb/20231010/207cebcb0cd88a5a020dd048db4b3284e.jpg" alt=""> -->
                                          <div class="popup-box-list-right-title">
                                            <div class="popup-box-list-right-title-name">
                                              {{ item.name }}
                                              <span v-if="!~item.cardId.indexOf('$') && !~item.cardId.indexOf('#')">好友</span>
                                            </div>
                                            <template v-if="item.team_name && (~item.cardId.indexOf('$') || ~item.cardId.indexOf('#'))">
                                              <i :style="{
                                                color: ~item.cardId.indexOf('#') ? '#FC7C14' : '#49BBFB'
                                              }">{{ item.team_name }}</i>
                                            </template>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                    <div class="popup-more" v-else>
                                      <img src="@/assets/zhixing/no-more.png"
                                        style="width: 200px;height: 200px;" alt="">
                                      <span>{{ chatingSession?.inSession ? '搜索无数据' : '暂无人员'}}</span>
                                    </div>
                                  </div>
                                </div>
                              </template>
                          </t-popup>
                        </div>
                      </div>
                      <div class="user-container-line"></div>
                      <div class="user-container-right">
                        <img class="add-addpend" src="@renderer/assets/zhixing/icon_addpend.svg" alt=""
                        @click="selectMemberVisible = true">
                      </div>
                    </div>
                  </t-form-item>
                  <t-form-item label="" name="noticeTyp" class="form-item">
                    <div class="form-item-label-box">
                      <div class="form-item-label">{{ t('zx.remind.frequency') }}</div>
                    </div>
                    <div style="display:flex;flex-direction:column;width: 100%;gap:8px;">
                      <t-select v-replace-svg  v-model="formData.noticeTyp"
                      :placeholder="t('zx.remind.frequencyTip')"
                      :disabled="isJudgmentRules"
                      >
                        <t-option v-for="item in frequencyOptions" :key="item.value" :value="item.value"
                          :label="item.label">
                          <template #content>
                            <t-radio :checked="formData.noticeTyp === item.value">{{ item.label }}</t-radio>
                          </template>
                        </t-option>
                      </t-select>
                      <t-select v-replace-svg  v-if="showDatePicker('EVERY_MONTH')" v-model="formData.day" class="date-picker"
                        :placeholder="t('zx.remind.frequencyTip')" :disabled="isJudgmentRules">
                        <template #suffixIcon>
                          <img style="height:16px;" src="@/assets/zhixing/icon_date_picker.png">
                        </template>
                        <t-option v-for="item in 31" :key="item" :value="item"
                          :label="item + t('zx.remind.day')"></t-option>
                      </t-select>
                      <t-select v-replace-svg  v-else-if="showDatePicker('EVERY_WEEK')"
                        :multiple="true" v-model="formData.week" class="date-picker" :class="{'week-picker': formData.week.length}"
                        :placeholder="t('zx.remind.frequencyTip')" :disabled="isJudgmentRules" requiredMark>
                        <template #valueDisplay="{ value }">
                          <span v-if="value">{{ value.map(v=>v.label).join('、') }}</span>
                          <span v-else style="color: #b1b8c3;">{{ t('zx.remind.frequencyTip') }}</span>
                        </template>
                        <template #suffixIcon>
                          <img style="height:16px;" src="@/assets/zhixing/icon_date_picker.png">
                        </template>
                        <t-option class="week-box-list-option" v-for="item in weekOptions" :key="item.value"
                        :value="item.value" :label="item.label"></t-option>
                      </t-select>
                      <t-cascader v-else-if="showDatePicker('EVERY_YEAR')" v-model="formData.year" :options="yearOptions"
                        class="date-picker" :disabled="isJudgmentRules">
                        <template #valueDisplay="{ value, selectedOptions }">
                          <div v-if="value">
                            <span>{{ selectedOptions[0]?.displayValue }}</span>
                          </div>
                        </template>
                      </t-cascader>
                    </div>
                  </t-form-item>
                  <t-form-item label="" name="time" class="form-item">
                    <div class="form-item-label-box">
                      <div class="form-item-label"><span style="color: red">*</span>{{ t('zx.remind.timeDate') }}</div>
                    </div>
                    <div class="form-item-time-box">
                      <t-date-picker v-if="showDatePicker('ONCE') || (isRepeatRemind && editDeleteRemind === 'only')"
                      enable-time-picker
                      format="YYYY-MM-DD HH:mm"
                      style="width: 100%;"
                      :time-picker-props="{
                        steps: [1,5,1],
                        format: 'HH:mm'
                      }"
                      allow-input clearable class="date-picker" v-model="formData.date"
                      :disableDate="{ before: moment().add(-1, 'day').format('YYYY-MM-DD HH:mm') }" />
                      <!-- <t-date-picker v-if="showDatePicker('ONCE')" class="date-picker" v-model="formData.date"
                        value-type="YYYY-MM-DD" :disableDate="{ before: moment().add(-1, 'day').format('YYYY-MM-DD') }" /> -->
                      <!-- <t-select v-replace-svg  v-else-if="showDatePicker('EVERY_MONTH')" v-model="formData.day" class="date-picker"
                        :placeholder="t('zx.remind.frequencyTip')">
                        <template #suffixIcon>
                          <img style="height:16px;" src="@/assets/zhixing/icon_date_picker.png">
                        </template>
                        <t-option v-for="item in 31" :key="item" :value="item"
                          :label="item + t('zx.remind.day')"></t-option>
                      </t-select>
                      <t-select v-replace-svg  v-else-if="showDatePicker('EVERY_WEEK')"
                        :multiple="true" v-model="formData.week" class="date-picker"
                        :placeholder="t('zx.remind.frequencyTip')">
                        <template #valueDisplay="{ value }">
                          <div v-if="value"
                          style="overflow: hidden;text-overflow: ellipsis; white-space: nowrap;height: 32px;
                          max-width: 172px;line-height: 32px;">
                          {{ value.map(v=>v.label).join('、') }}</div>
                          <span v-else style="color: #b1b8c3;">{{ t('zx.remind.frequencyTip') }}</span>
                        </template>
                        <template #suffixIcon>
                          <img style="height:16px;" src="@/assets/zhixing/icon_date_picker.png">
                        </template>
                        <t-option class="week-box-list-option" v-for="item in weekOptions" :key="item.value"
                        :value="item.value" :label="item.label"></t-option>
                      </t-select> -->
                      <!-- <t-select v-replace-svg  v-else-if="showDatePicker('EVERY_WEEK')" v-model="formData.week" class="date-picker"
                        :placeholder="t('zx.remind.frequencyTip')">
                        <template #suffixIcon>
                          <img style="height:16px;" src="@/assets/zhixing/icon_date_picker.png">
                        </template>
                        <t-option v-for="item in weekOptions" :key="item.value" :value="item.value"
                          :label="item.label"></t-option>
                      </t-select> -->
                      <!-- <t-cascader v-else-if="showDatePicker('EVERY_YEAR')" v-model="formData.year" :options="yearOptions"
                        class="date-picker" clearable>

                      </t-cascader> -->
                      <t-time-picker v-if="!showDatePicker('ONCE') && !(isRepeatRemind && editDeleteRemind === 'only')" :class="['time-picker', showTimePicker ? 'timePickerWidth' : '']"
                        v-model="formData.time" format="HH:mm" :steps="[1, 5, 1]" />
                    </div>

                  </t-form-item>
                  <!-- 重复提醒 -->
                  <!-- <t-form-item label="" name="repeatTyp" class="form-item">
                <div class="form-item-label-box">
                  <div class="form-item-label">{{ t('zx.remind.frequencyRepeat') }}</div>
                </div>
                <t-select v-replace-svg  v-model="formData.repeatTyp" :placeholder="t('zx.remind.notHave')" multiple>
                  <t-option v-for="item in frequencyRepeatOptions" :key="item.value" :value="item.value"
                    :label="item.label"></t-option>
                  <template #valueDisplay="{ value }">
                    {{ value.length ? `${returnTips(value)}${t('zx.remind.againRemind')}` : '' }}
                  </template>
                </t-select>
              </t-form-item> -->
                </t-form>
                <div class="card-box" v-if="formData.content" @click="openCard">
                  <img class="avatar" :src="formData.content?.icon" alt="">
                  <div class="text">{{ formData.content?.title }}</div>
                </div>
              </div>
              <div class="drawerFooter">
                <t-button theme="default" variant="outline" @click="isDetail = true"
                  style="margin-right: 8px;width: 80px;">取消</t-button>
                <t-button theme="primary" variant="base" @click="onSubmit(false, false, false)"
                  style="width: 80px">保存</t-button>
                <!-- <t-button :disabled="showSubmit" theme="primary" variant="base" @click="onSubmit">提交</t-button> -->
              </div>
            </div>
          </template>
        </div>
      </t-drawer>
    </div>
    <!-- 有后续提醒 -->
    <div v-else>
      <div v-if="!isFirstInit" class="drawerBox width-box" :class="{
        'not-from-chat': !fromChat,
        'edit-container': !isDetail,
      }">
        <!-- 详情（知行-待办） -->
        <template v-if="isDetail">
          <div v-if="remindDetailData?.deleteAat" style="height: 100%;">
            <div class="drawerHeader" style="display:flex;justify-content:end;">
              <iconpark-icon name="iconerror" style="font-size:20px;color:#828DA5;" @click="close"></iconpark-icon>
            </div>
            <div class="more-item" style="height:calc(100% - 68px);">
              <no-data style="display:flex;flex-direction:column;align-items: center;" title="内容已删除" />
            </div>
          </div>
          <template v-else>
            <div class="drawerHeader">
              <div>
                <t-button v-if="isMentionUser && userDone" variant="outline" theme="default" disabled>{{
                  t('zx.remind.finished') }}</t-button>
                <t-button v-if="isMentionUser && !userDone" variant="outline" theme="primary" @click="finishRemind">{{
                  t('zx.remind.finish') }}</t-button>
                <t-button v-if="!isMentionUser && !isOwner" variant="outline" theme="default" @click="saveToMy">{{
                  t('zx.remind.saveMyRemind') }}</t-button>
                <!-- <t-button v-if="isOwner && formData.noticeTyp !== 'ONCE'" variant="outline" theme="default"
                  @click="editClick">{{ t('zx.remind.editRemind') }}</t-button> -->
              </div>
              <div class="act-icon-group">
                <template v-if="isOwner && !remindDetailData.mentionUsers?.some(v=> !!Number(v.finishAt))">
                  <iconpark-icon name="iconedit" class="icon" v-if="formData.noticeTyp !== 'ONCE'" @click="onRepeatEdit"></iconpark-icon>
                  <iconpark-icon name="iconedit" class="icon" v-else-if="!userDone" @click="isDetail = false"></iconpark-icon>
                </template>
                <!-- <iconpark-icon name="iconedit" class="icon" v-if="isOwner" @click="saveToMy"></iconpark-icon> -->
                <iconpark-icon name="iconshare2" class="icon" v-if="isOwner || isMentionUser"
                  @click="shareClick"></iconpark-icon>
                <iconpark-icon name="icondelete" class="icon" @click="deleteClick"></iconpark-icon>
                <iconpark-icon name="iconerror" class="icon" @click="close"></iconpark-icon>
              </div>
            </div>
            <div class="drawerFrom" v-if="remindDetailData && remindDetailData?.fromName && getOpenid() === remindDetailData?.owner">
              <img src="@renderer/assets/img/icon_info.svg"
                style="width: 20px;height: 20px;margin-right: 8px;vertical-align: middle;">
              来自{{ remindDetailData?.typ === 'GROUP' ? '群聊' : '单聊' }}： {{ remindDetailData?.fromName }}
              <span class="drawerFrom_name" @click="goToIm">点击查看</span>
            </div>
            <div class="drawerContent">
              <t-form ref="formRef" class="formDisabledRef" :rules="formRules" :data="formData" labelWidth="80"
                :requiredMark="false">
                <t-form-item label="" name="title" class="form-item title-item">
                  <t-textarea readonly v-model="formData.title" placeholder="" :maxlength="255" :autosize="true" />
                </t-form-item>
                <t-form-item label="" name="desc" class="form-item desc-item" v-if="formData.desc">
                  <t-textarea readonly v-model="formData.desc" placeholder="描述" :maxlength="255"
                    style="border: 0px solid #111f40;border-radius: 4px;" :autosize="{ maxRows: 4 }" />
                </t-form-item>
                <!-- 开始 -->
                <t-form-item class="form-item" v-if="formData?.noticeMatters && formData?.noticeMatters.length">
                  <div style="width: 100%;">
                    <div class="form-item-title">
                      <!-- <img src="@renderer/assets/zhixing/icon-item-big.svg" alt="" style="width:15px;height: 15px;margin-right: 5px;"> -->
                      <iconpark-icon name="iconlist"
                        style="font-size:20px;margin-right: 3px;color:rgba(130, 141, 165, 1)"></iconpark-icon>
                      提醒事项
                    </div>
                    <div class="remind-item" v-for="(item, index) in formData.noticeMatters" :key="item.name + index">
                      <div class="f-align">
                        <t-checkbox v-model="item.finish" @change="(v) => updateMatters(item)"></t-checkbox>
                        <span>{{ item.name }}</span>
                      </div>
                      <div class="split" v-if="item.finishUser"></div>
                      <div class="f-align" v-if="item.finishUser">
                        <avatar class="avatar-icon" :imageUrl="item.finishUser.avatar" :userName="item.finishUser.name"
                          avatarSize="20px" />
                        <span class="user-name">{{ item.finishUser.name }}</span>
                        <span class="finish-time">完成于{{ moment(Number(item.finishUser.finishAt || item.finishUser.finish_at)).format('YYYY-MM-DD HH:mm') }}</span>
                      </div>
                    </div>
                  </div>
                </t-form-item>
                <t-form-item class="form-item">
                  <div>
                    <div class="form-item-title" style="margin-bottom:4px;">
                      <!-- <img src="@renderer/assets/zhixing/icon-item-big.svg" alt="" style="width:14px;height: 13px;margin-right: 4px;"> -->
                      <iconpark-icon name="iconpeople"
                        style="margin-right: 3px;font-size:20px;color:rgba(130, 141, 165, 1)"></iconpark-icon>
                      提醒到人
                    </div>
                    <div style="margin-left: 23px;display: flex;flex-wrap: wrap;">
                      <div class="user-info" v-for="item in formData.mentionUsers" :key="item.openid">
                        <avatar class="avatar-icon" :imageUrl="item.avatar" :userName="item.name" avatarSize="44px" />
                        <div class="user-name">{{ item.name }}</div>
                        <img v-if="item.finishAt !== '0'" src="@renderer/assets/zhixing/icon-suc-small.png" alt="" class="icon">
                      </div>
                    </div>
                  </div>
                </t-form-item>
                <!-- 结束 -->
                <t-form-item name="noticeTyp" class="form-item">
                  <div class="form-item-title">
                    <!-- <img src="@renderer/assets/zhixing/icon-item-big.svg" alt="" style="width:14px;height: 13px;margin-right: 4px;"> -->
                    <iconpark-icon name="iconsubmit"
                      style="font-size:20px;margin-right: 3px;color:rgba(130, 141, 165, 1)"></iconpark-icon>
                    {{ t('zx.remind.frequency') }}
                  </div>
                  <div style="display: flex;margin-left:5px;padding-top:16px;">
                    <div style="padding:0 0 0 11px;">{{ checkFrequency(formData.noticeTyp) }}</div>
                    <!-- <t-select v-replace-svg  v-model="formData.noticeTyp" readonly :showArrow="false" placeholder="" borderless>
                      <t-option v-for="item in frequencyOptions" :key="item.value" :value="item.value" :label="item.label">
                        <template #content>
                          <t-radio :checked="formData.noticeTyp === item.value">{{ item.label }}</t-radio>
                        </template>
                      </t-option>
                    </t-select> -->
                  </div>

                </t-form-item>
                <t-form-item name="time" class="form-item">
                  <div class="form-item-time-box">
                    <!-- <t-date-picker v-if="showDatePicker('ONCE')" disabled class="date-picker" v-model="formData.date" value-type="YYYY-MM-DD" :disableDate="{ before: moment().add(-1,'day').format('YYYY-MM-DD') }">
                        <template #suffixIcon>
                        </template>
                      </t-date-picker> -->
                    <div class="form-item-title" style="padding-top:16px;">
                      <!-- <img src="@renderer/assets/zhixing/icon-item-big.svg" alt="" style="width:14px;height: 13px;margin-right: 4px;"> -->
                      <iconpark-icon name="icondate"
                        style="font-size:20px;margin-right: 3px;color:rgba(130, 141, 165, 1)"></iconpark-icon>
                      {{ t('zx.remind.timeDate') }}
                    </div>
                    <div class="form-item-time-box-time-show"
                      :style="{ paddingTop: showDatePicker('ONCE') ? '17px' : '15px' }">
                      <!-- 场景1.3注释掉 -->
                      <span style="margin-left:15px;">{{ formData.dateTime }}</span>
                    </div>
                  </div>

                </t-form-item>
                <!-- 重复提醒 -->
                <t-form-item class="form-item" v-if="formData.content">
                  <div class="form-item-title">
                    <iconpark-icon name="iconlink"
                      style="font-size:20px;margin-right:3px;color:rgba(130, 141, 165, 1)"></iconpark-icon>
                    {{ t('zx.remind.associationBusiness') }}
                  </div>
                </t-form-item>
              </t-form>
              <div class="card-box" v-if="formData.content" @click="openCard">
                <img class="avatar" :src="formData.content?.icon" alt="">
                <div class="text">{{ formData.content?.title }}</div>
              </div>
            </div>
          </template>
        </template>
        <!-- 编辑提醒详情 -->
        <template v-else>
          <div class="edit-container">
            <div class="drawerHeader">
              <div class="title">
                <!-- <t-button disabled variant="outline" theme="primary" v-if="zhixingStore.remindDetailDialogData.props?.done">{{ t('zx.remind.finished') }}</t-button>
                <template v-else>
                  <t-button variant="outline" theme="primary" @click="finishClick">{{ t('zx.remind.finish') }}</t-button>
                  <t-button variant="outline" theme="default" v-if="isRepeatRemind" @click="editClick">{{ t('zx.remind.editRemind') }}</t-button>
                </template> -->
                编辑提醒详情
              </div>
              <div @click="isDetail = true">
                <img style="height:16px;cursor: pointer;" src="@/assets/im/im_close.png">
              </div>
            </div>
            <div class="drawerContent" v-if="showFormRef">
              <t-form ref="formRef" :rules="formRules" :data="formData" labelWidth="0" resetType="initial">
                <t-form-item label="" name="title" requiredMark class="form-item">
                  <div class="form-item-label-box">
                    <div style="color: red">*</div>
                    <div class="form-item-label">{{ t('zx.remind.title') }}</div>
                  </div>
                  <t-input v-model="formData.title" :placeholder="t('zx.remind.inputTip')" :maxlength="255" clearable />
                </t-form-item>
                <t-form-item :label="t('zx.remind.desc')" name="desc" class="form-item">
                  <div class="form-item-label-box">
                    <div class="form-item-label">{{ t('zx.remind.desc') }}</div>
                  </div>
                  <t-textarea v-model="formData.desc" :placeholder="t('zx.remind.inputTip')" :maxlength="255"
                    :autosize="{ minRows: 3, maxRows: 5 }" />
                </t-form-item>
                <t-form-item class="form-item">
                  <div class="f-between" style="width: 100%">
                    <div class="form-item-label">提醒事项</div>
                    <img class="add-addpend" src="@renderer/assets/zhixing/icon_addpend.svg" alt=""
                      @click="addNoticeMatter">
                  </div>
                  <draggable :list="formData.noticeMatters" animation="300" itemKey="index" @end="onEnd"
                    style="width: 100%">
                    <template #item="{ element, index }">
                      <div class="f-between" style="margin-top: 12px">
                        <iconpark-icon name="icondrag"></iconpark-icon>
                        <t-input style="margin: 0 12px;" v-model="element.name" :maxlength="100"></t-input>
                        <img class="add-addpend" src="@renderer/assets/zhixing/icon_delete.svg" alt=""
                          @click="removeNoticeMatter(index)">
                      </div>
                    </template>
                  </draggable>
                </t-form-item>
                <t-form-item label="" name="mentionUsers" class="form-item" requiredMark>
                  <div class="f-between" style="width: 100%">
                    <div class="form-item-label">
                      <span style="color: red">*</span>提醒到人
                    </div>
                    <img class="add-addpend" src="@renderer/assets/zhixing/icon_addpend.svg" alt=""
                      @click="selectMemberVisible = true">
                  </div>
                  <div class="user-container">
                    <div class="user-info" v-for="item in formData.mentionUsers" :key="item.openid">
                      <avatar class="avatar-icon" :imageUrl="item.avatar" :userName="item.name" avatarSize="44px" />
                      <div class="user-name">{{ item.name }}</div>
                      <img v-if="item.cardId !== getOpenid()" src="@renderer/assets/zhixing/icon-close.svg" alt=""
                        class="icon" @click="removeUser(item)">
                    </div>
                  </div>
                </t-form-item>
                <t-form-item label="" name="noticeTyp" class="form-item">
                  <div class="form-item-label-box">
                    <div class="form-item-label">{{ t('zx.remind.frequency') }}</div>
                  </div>
                  <div style="display:flex;flex-direction:column;width: 100%;gap:8px;">
                    <t-select v-replace-svg  v-model="formData.noticeTyp"
                    :placeholder="t('zx.remind.frequencyTip')"
                    :disabled="isJudgmentRules">
                      <t-option v-for="item in frequencyOptions" :key="item.value" :value="item.value" :label="item.label">
                        <template #content>
                          <t-radio :checked="formData.noticeTyp === item.value">{{ item.label }}</t-radio>
                        </template>
                      </t-option>
                    </t-select>
                    <t-select v-replace-svg  v-if="showDatePicker('EVERY_MONTH')" v-model="formData.day" class="date-picker"
                      :placeholder="t('zx.remind.frequencyTip')"
                              :disabled="isJudgmentRules">
                      <template #suffixIcon>
                        <img style="height:16px;" src="@/assets/zhixing/icon_date_picker.png">
                      </template>
                      <t-option v-for="item in 31" :key="item" :value="item"
                        :label="item + t('zx.remind.day')"></t-option>
                    </t-select>
                    <t-select v-replace-svg  v-else-if="showDatePicker('EVERY_WEEK')"
                      :multiple="true" v-model="formData.week" class="date-picker" :class="{'week-picker': formData.week.length}"
                      :placeholder="t('zx.remind.frequencyTip')" :disabled="isJudgmentRules" requiredMark>
                      <template #valueDisplay="{ value }">
                        <span v-if="value">{{ value.map(v=>v.label).join('、') }}</span>
                        <span v-else style="color: #b1b8c3;">{{ t('zx.remind.frequencyTip') }}</span>
                      </template>
                      <template #suffixIcon>
                        <img style="height:16px;" src="@/assets/zhixing/icon_date_picker.png">
                      </template>
                      <t-option class="week-box-list-option" v-for="item in weekOptions" :key="item.value"
                      :value="item.value" :label="item.label"></t-option>
                    </t-select>

                    <t-cascader
                      v-else-if="showDatePicker('EVERY_YEAR')"
                      v-model="formData.year"
                      :options="yearOptions"
                      :disabled="isJudgmentRules"
                      class="date-picker"
                    >
                      <template #valueDisplay="{ value, selectedOptions }">
                        <div v-if="value">
                          <span>{{ selectedOptions[0]?.displayValue }}</span>
                        </div>
                      </template>
                    </t-cascader>
                  </div>
                </t-form-item>
                <t-form-item label="" name="time" class="form-item">
                  <div class="form-item-label-box">
                    <div class="form-item-label"><span style="color: red">*</span>{{ t('zx.remind.timeDate') }}</div>
                  </div>
                  <div class="form-item-time-box">
                    <t-date-picker v-if="showDatePicker('ONCE') || (isRepeatRemind && editDeleteRemind === 'only')"
                    enable-time-picker
                    format="YYYY-MM-DD HH:mm"
                    style="width: 100%;"
                    :time-picker-props="{
                      steps: [1,5,1],
                      format: 'HH:mm'
                    }"
                    allow-input clearable class="date-picker" v-model="formData.date"
                    :disableDate="{ before: moment().add(-1, 'day').format('YYYY-MM-DD HH:mm') }" />
                    <!-- <t-date-picker v-if="showDatePicker('ONCE')" class="date-picker" v-model="formData.date"
                      value-type="YYYY-MM-DD" :disableDate="{ before: moment().add(-1, 'day').format('YYYY-MM-DD') }" /> -->
                    <!-- <t-select v-replace-svg  v-else-if="showDatePicker('EVERY_MONTH')" v-model="formData.day" class="date-picker"
                      :placeholder="t('zx.remind.frequencyTip')">
                      <template #suffixIcon>
                        <img style="height:16px;" src="@/assets/zhixing/icon_date_picker.png">
                      </template>
                      <t-option v-for="item in 31" :key="item" :value="item"
                        :label="item + t('zx.remind.day')"></t-option>
                    </t-select>
                    <t-select v-replace-svg  v-else-if="showDatePicker('EVERY_WEEK')" v-model="formData.week" class="date-picker"
                      :placeholder="t('zx.remind.frequencyTip')">
                      <template #suffixIcon>
                        <img style="height:16px;" src="@/assets/zhixing/icon_date_picker.png">
                      </template>
                      <t-option v-for="item in weekOptions" :key="item.value" :value="item.value"
                        :label="item.label"></t-option>
                    </t-select>
                    <t-cascader v-else-if="showDatePicker('EVERY_YEAR')" v-model="formData.year" :options="yearOptions"
                      class="date-picker" clearable>

                    </t-cascader> -->
                    <t-time-picker v-if="!showDatePicker('ONCE') && !(isRepeatRemind && editDeleteRemind === 'only')" :class="['time-picker', showTimePicker ? 'timePickerWidth' : '']"
                      v-model="formData.time" format="HH:mm" :steps="[1, 5, 1]" />
                  </div>

                </t-form-item>
                <!-- 重复提醒 -->
                <!-- <t-form-item label="" name="repeatTyp" class="form-item">
                <div class="form-item-label-box">
                  <div class="form-item-label">{{ t('zx.remind.frequencyRepeat') }}</div>
                </div>
                <t-select v-replace-svg  v-model="formData.repeatTyp" :placeholder="t('zx.remind.notHave')" multiple>
                  <t-option v-for="item in frequencyRepeatOptions" :key="item.value" :value="item.value"
                    :label="item.label"></t-option>
                  <template #valueDisplay="{ value }">
                    {{ value.length ? `${returnTips(value)}${t('zx.remind.againRemind')}` : '' }}
                  </template>
                </t-select>
              </t-form-item> -->
              </t-form>
              <div class="card-box" v-if="formData.content" @click="openCard">
                <img class="avatar" :src="formData.content?.icon" alt="">
                <div class="text">{{ formData.content?.title }}</div>
              </div>
            </div>
            <div class="drawerFooter">
              <t-button theme="default" variant="outline" @click="isDetail = true"
                style="margin-right: 8px;width: 80px;">取消</t-button>
              <t-button theme="primary" variant="base" @click="onSubmit(false, false, false)"
                style="width: 80px">保存</t-button>
              <!-- <t-button :disabled="showSubmit" theme="primary" variant="base" @click="onSubmit">提交</t-button> -->
            </div>
          </div>
        </template>
      </div>
    </div>

    <selectMember v-model:visible="selectMemberVisible" :change-menus="true" :select-list="selectListId" :extend-from="['noPlatform']"
      @confirm="noticeUserList" :attach="'body'" />

    <t-dialog v-model:visible="deleteVisible" attach="body" :on-confirm="onConfirmDelete">
      <template #closeBtn>
        <img src="@renderer/assets/zhixing/icon-confirm-close.svg" alt="" @click="deleteVisible = false">
      </template>
      <template #header>
        <div>{{ isRepeatRemind ? t('zx.remind.deleteRemindTip') : t('zx.remind.deleteTheRemindTip') }}</div>
      </template>
      <template #body>
        <div v-if="isRepeatRemind">
          <div class="delete-remind-text">{{ isCreator() ? t('zx.remind.deleteRepeatRemindTip') : t('zx.remind.deleteRepeatRemindTipWrong') }}</div>
          <t-radio-group default-value="onlyThis" :onChange="radioChange"
            style="flex-direction: column;align-items: flex-start;">
            <t-radio value="onlyThis">{{ t('zx.remind.onlyTheRemindTip') }}</t-radio>
            <t-radio value="otherAll">{{ t('zx.remind.notYetTimeTip') }}</t-radio>
          </t-radio-group>
        </div>
        <div class="delete-remind-text" v-else>{{ isCreator() ? t('zx.remind.deleteDataTip') : t('zx.remind.deleteDataTipWrong') }}</div>
      </template>
    </t-dialog>
    <edit-remind-dialog
    v-if="isEditRemindDialog"
    v-model:visible="isEditRemindDialog"
    :state="remindState"
    @confirm="onEditRemindConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia';
import selectMember from "@renderer/components/rk-business-component/select-member/common-add-members.vue";
import draggable from "vuedraggable";
import avatar from '@renderer/components/kyy-avatar/index.vue';
import { ref, watch, reactive, computed, onMounted, nextTick } from 'vue';
import { useZhixingStore } from '@/views/zhixing/store';
import moment from "moment";
import { DialogPlugin, MessagePlugin } from "tdesign-vue-next";
import { getOpenid, getProfilesInfo } from "@/utils/auth";
import { getImCardIds } from "@renderer/utils/auth";
import { useChatExtendStore } from "@/views/message/service/extend";
import { useChatActionStore } from '@/views/message/service/actionStore';
import _ from 'lodash';
import to from "await-to-js";
import {
  addNotice,
  editNotice,
  getNotice,
  deleteNotice,
  deleteNoticeAfter,
  deleteNoticeAfterNew,
  editNoticeAfter
} from "@/api/zhixing/api/remind";
import { useMessageStore } from '@/views/message/service/store';
import { openChat } from "@/utils/share";
import { useI18n } from 'vue-i18n';
import NoData from '@renderer/views/message/scene/ranking/no-data.vue';
import selectData from '@/components/selectMember/data';
const msgStore = useMessageStore();
import { getGroupMemberListApi, getPrivateChatApi } from "@/api/im/api";
const { chatingSession } = storeToRefs(msgStore);
import EditRemindDialog from "@/views/zhixing/components/remind-common/edit-remind-dialog.vue";

const { t } = useI18n();

const props = defineProps({
  isDialog: {
    type: Boolean,
    default: true,
  },
  openid: {
    type: String,
    default: ''
  },
  fromChat: {
    type: Boolean,
    default: false
  }
})
const emits = defineEmits(['refresh', 'update', 'delete', 'close']);
const selectMemberVisible = ref(false);
const selectListId = ref([]);
const selfInfo = getProfilesInfo();
const zhixingStore = useZhixingStore();
const showSubmit = computed(() => !formData.title)
const isDetail = ref(true);
const isOwner = computed(() => zhixingStore.remindDetailDialogData.props?.owner === getOpenid());
const formData = reactive({
  title: '',
  desc: '',
  noticeTyp: 'ONCE',
  dateTime: '',
  week: [],
  day: 1,
  year: '1-1',
  // date: moment().format('YYYY-MM-DD'),
  date: moment().format('YYYY-MM-DD HH:mm'),
  time: moment().add(5, "minutes").format('HH:mm'),
  repeatTyp: '',
  knockAt: '',
  openid: '',
  content: null,
  noticeMatters: [],
  mentionUsers: [],
  chooseWeekdays:[]
})
const isMentionUser = computed(() => {
  if (formData?.mentionUsers) {
    return !!~formData.mentionUsers.findIndex(v => getImCardIds().includes(v.cardId));
  }
  return false;
});
const userDone = computed(() => {
  if (formData?.mentionUsers) {
    const user = formData.mentionUsers.find(v => getImCardIds().includes(v.cardId));
    return user?.finishAt !== '0';
  }
  return false;
})
const isRepeatRemind = computed(() => zhixingStore.remindDetailDialogData.props?.noticeTyp !== 'ONCE')
const showFormRef = ref(false)
const deleteVisible = ref(false)
const deleteCheckedForm = reactive({
  onlyThis: true,
  otherAll: false
})
const remindDetailData = ref({})
const formRules = ref({
  // title: [{ required: true, message: t('zx.remind.required') }],
  noticeTyp: [{ required: true, message: t('zx.remind.required') }],
  date: [{ required: true, message: t('zx.remind.required') }],
  time: [
    { required: true, message: t('zx.remind.required') },
    {
      trigger: 'blur', validator: () => {
        let label = frequencyOptions.value.find(item => formData.noticeTyp === item.value)?.formDataLabel
        return Boolean(formData[label] && formData.time)
      }, message: t('zx.remind.required'), type: 'warning'
    }
  ],
});
const formRef = ref(null)
const formDisabledRef = ref(null)
const isFirstInit = ref(true)
const frequencyOptions = ref([
  { label: t('zx.remind.ONCE'), value: 'ONCE', formDataLabel: 'date' },
  { label: t('zx.remind.EVERY_DAY'), value: 'EVERY_DAY', formDataLabel: 'time' },
  { label: t('zx.remind.EVERY_WEEK'), value: 'EVERY_WEEK', formDataLabel: 'week' },
  { label: t('zx.remind.EVERY_MONTH'), value: 'EVERY_MONTH', formDataLabel: 'day' },
  { label: t('zx.remind.EVERY_YEAR'), value: 'EVERY_YEAR', formDataLabel: 'year' },
  { label: t('zx.remind.EVERY_WORKDAY'), value: 'EVERY_WORKDAY', formDataLabel: 'time' },
  { label: t('zx.remind.EVERY_MON_SAT'), value: 'EVERY_MON_SAT', formDataLabel: 'time' },
  { label: t('zx.remind.EVERY_SUN_FRI'), value: 'EVERY_SUN_FRI', formDataLabel: 'time' },
])
const frequencyRepeatOptions = ref([
  { label: '5' + t('zx.remind.afterMinute'), value: 'AFTER_5M' },
  { label: '15' + t('zx.remind.afterMinute'), value: 'AFTER_15M' },
  { label: '30' + t('zx.remind.afterMinute'), value: 'AFTER_30M' },
  { label: '1' + t('zx.remind.afterHour'), value: 'AFTER_1H' },
  { label: '2' + t('zx.remind.afterHour'), value: 'AFTER_2H' },
  { label: '1' + t('zx.remind.afterDay'), value: 'AFTER_1D' },
  { label: '2' + t('zx.remind.afterDay'), value: 'AFTER_2D' },
])
const weekOptions = ref([
  { label: t('zx.remind.mon'), value: 1 },
  { label: t('zx.remind.tue'), value: 2 },
  { label: t('zx.remind.wed'), value: 3 },
  { label: t('zx.remind.thu'), value: 4 },
  { label: t('zx.remind.fri'), value: 5 },
  { label: t('zx.remind.sat'), value: 6 },
  { label: t('zx.remind.sun'), value: 7 },
]);

// 判断是否提示
const isRemindTip = ref(true);
// 判断是否刷新
const isRefresh = ref(false);

// 最近列表人列表
const recentList = ref<any[]>([]);
const pickList = ref<any[]>([]);
const pickListData = ref<any[]>([]);

// 编辑提醒弹窗
const isEditRemindDialog = ref<boolean>(false);
// 编辑参数
const remindState = ref<"edit" | "creatorDel" | "remindDel">('edit');

const yearOptions = computed(() => {
  let m = Array.from(Array(12).keys(), n => n + 1)
  return m.map(items => {
    // 设置每月天数
    let day = [2].includes(items) ? 28 : [1, 3, 5, 7, 8, 10, 12].includes(items) ? 31 : 30
    let d = Array.from(Array(day).keys(), n => n + 1)
    return {
      label: items + t('zx.remind.month'),
      value: items,
      children: d?.map(item => {
        return {
          label: item + t('zx.remind.day'),
          value: items + '-' + item,
          displayValue: `${items < 10 ? ('0' + items) : items}${t("zx.remind.month")}${item < 10 ? ('0' + item) : item}${t("zx.remind.day")}`
        }
      })
    }
  })
})

const isEditNoticeType = computed(() => remindDetailData.value?.noticeTyp !== 'ONCE')
const showDatePicker = (type) => {
  return formData.noticeTyp == type
}
const showTimePicker = computed(() => !['ONCE', 'EVERY_WEEK', 'EVERY_MONTH', 'EVERY_YEAR'].includes(formData.noticeTyp))
const returnTips = (value) => {
  return value?.map(item => item.label)?.join(';')
}
const _transData_knock_at = (data) => {
  let dataTimeObj = {
    label: 'date',
    value: null,
  }
  switch (data.noticeTyp) {
    case 'ONCE':
      dataTimeObj.label = 'date'
      dataTimeObj.value = moment(data.knockAt).format("YYYY-MM-DD")
      break;
    case 'EVERY_DAY':
      dataTimeObj.label = 'date'
      dataTimeObj.value = ''
      break;
    case 'EVERY_WEEK':
      dataTimeObj.label = 'week'
      dataTimeObj.value = moment(data.knockAt).day() === 0 ? 7 : moment(data.knockAt).day()
      break;
    case 'EVERY_MONTH':
      dataTimeObj.label = 'day'
      dataTimeObj.value = moment(data.knockAt).date()
      break;
    case 'EVERY_WORKDAY':
      dataTimeObj.label = 'date'
      dataTimeObj.value = ''
      break;
    case 'EVERY_MON_SAT':
      dataTimeObj.label = 'date'
      dataTimeObj.value = ''
      break;
    case 'EVERY_SUN_FRI':
      dataTimeObj.label = 'date'
      dataTimeObj.value = ''
      break;
    case 'EVERY_YEAR':
      dataTimeObj.label = 'year'
      dataTimeObj.value = moment(data.knockAt).month() + 1 + '-' + moment(data.knockAt).date()
      break;
    default:
      dataTimeObj.label = 'date'
      dataTimeObj.value = ''
      break;
  }
  return dataTimeObj
}
const format_knock_at = () => {
  let dateTime = null
  let today = moment().day() === 0 ? 7 : moment().day();
  switch (formData.noticeTyp) {
    case 'ONCE':
      dateTime = moment(formData.date).format("YYYY-MM-DD HH:mm")
      break;
    case 'EVERY_DAY':
      dateTime = moment().format("YYYY-MM-DD") + ' ' + formData.time
      break;
    case 'EVERY_WEEK':
      /**
       * 判断选择的周期是否大于当前时间-->如果大于当前时间，则需要再前基础上加7天(也就是下周)
       * 这儿需要改变逻辑-->如果选择了周一和周五-->而当前时间是周二那么提醒时间应该是怎么选择???
       */
      // 排序
      const weekList:any = formData.week.sort((a, b) => a - b) || [];
      // 如果查询的到就说明是当前时间
      const isWeek = weekList.includes(today);
      if (isWeek) {
        dateTime = moment().format("YYYY-MM-DD") + ' ' + formData.time;
      }else {
        // 查询是否有比当前时间大的-->如果有就知行有的数据, 如果没有就提前到下一周
        const weekIndex:number = weekList.findIndex((item:number) => item > today);
        if (weekIndex !== -1) {
          dateTime = moment().add(weekList[weekIndex] - today, 'd').format("YYYY-MM-DD") + ' ' + formData.time;
        }else {
          dateTime = moment().add(7 - (today - weekList[0]), 'd').format("YYYY-MM-DD") + ' ' + formData.time
        }
      }
      break;
    case 'EVERY_MONTH':
      dateTime = moment().format("YYYY-MM") + '-' + formData.day + ' ' + formData.time
      break;
    case 'EVERY_WORKDAY':
      // 处理周末
      if ([6, 7].includes(today)) {
        dateTime = moment().add(8 - today, 'd').format("YYYY-MM-DD") + ' ' + formData.time
      } else {
        dateTime = moment().format("YYYY-MM-DD") + ' ' + formData.time
      }
      break;
    case 'EVERY_MON_SAT':
      if ([7].includes(today)) {
        dateTime = moment().add(1, 'd').format("YYYY-MM-DD") + ' ' + formData.time
      } else {
        dateTime = moment().format("YYYY-MM-DD") + ' ' + formData.time
      }
      break;
    case 'EVERY_SUN_FRI':
      if ([6].includes(today)) {
        dateTime = moment().add(1, 'd').format("YYYY-MM-DD") + ' ' + formData.time
      } else {
        dateTime = moment().format("YYYY-MM-DD") + ' ' + formData.time
      }
      break;
    case 'EVERY_YEAR':
      dateTime = moment().format("YYYY") + '-' + formData.year + ' ' + formData.time
      break;
    default:
      dateTime = moment().format("YYYY-MM-DD") + ' ' + formData.time
      break;
  }
  console.log(dateTime)
  return new Date(dateTime).getTime()
}
const saveToMy = () => {
  console.log('saveToMy aaaa')
  zhixingStore.setRemindDetail(zhixingStore.remindDetailDialogData.props);
  useChatActionStore().onAddRemindNew();
  // onSubmit(false, true, true);
}
const close = () => {
  zhixingStore.remindDetailDialogData.props = null;
  zhixingStore.remindDetailDialogData.visible = false;
  useChatExtendStore().hideChatDialog();
  emits('close');
}
const openCard = () => {
  console.log('openCard', zhixingStore.remindDetailDialogData)
}
const finishClick = () => {
  console.log('finishClick')
  onSubmit(true)
}
const editClick = () => {
  console.log('editClick')
  zhixingStore.addRemindDialogData.props = remindDetailData.value
  zhixingStore.addRemindDialogData.type = 'edit'
  zhixingStore.addRemindDialogData.visible = true
}
const shareClick = () => {
  zhixingStore.shareSessionDialogData.readonly = true
  zhixingStore.shareSessionDialogData.visible = true
}
const deleteClick = () => {
  if (!isRepeatRemind.value) return (deleteVisible.value = true);
  remindState.value = isCreator() ? 'creatorDel' : 'remindDel';
  isEditRemindDialog.value = true;
  // deleteVisible.value = true
}
const itemBlur = (value, context) => {
  console.log('itemBlur1', value, context)
  if (isFirstInit.value) return;
  if (!value) {
    onSubmit(false, false);
  }
}
const itemBlurTitle = (value, context) => {
  console.log('itemBlur2', value, context)
  if (isFirstInit.value) return;
  if (value === remindDetailData.value?.title) return;
  onSubmit(false, false);
}
const itemBlurDesc = (value, context) => {
  console.log('itemBlur2', value, context)
  if (isFirstInit.value) return;
  if (value === remindDetailData.value?.desc) return;
  onSubmit(false, false);
}
const itemBlurTime = (value, context) => {
  console.log('itemBlur', value, context)
  if (isFirstInit.value) return;
  onSubmit(false, true);
}
const finishRemind = async () => {
  let data = zhixingStore.remindDetailDialogData.props;
  data.done = true;
  data.mentionUsers = data.mentionUsers.map(v => {
    if (getImCardIds().includes(v.cardId)) {
      v.finishAt = Date.now();
    }
    return v;
  });
  let res = await editNotice(data)
  zhixingStore.remindModelRefresh = !zhixingStore.remindModelRefresh//提醒单独模块数据刷新
  isRemindTip.value = false;
  close();
}
const updateMatters = (item) => {
  const user = formData.mentionUsers.find(v => getImCardIds().includes(v.cardId));
  if (item.finish) {
    const user = formData.mentionUsers.find(v => getImCardIds().includes(v.cardId));
    item.finishUser = _.cloneDeep(user);
    item.finishUser.finishAt = Date.now();
  } else {
    item.finishUser = null;
  }
  isRemindTip.value = false;
  onSubmit(false, false, false, false);
}
const addNoticeMatter = () => {
  formData.noticeMatters.push({ name: '' })
}
const onEnd = () => {

}
const removeNoticeMatter = (index) => {
  formData.noticeMatters.splice(index, 1)
}
const removeUser = (user) => {
  formData.mentionUsers = formData.mentionUsers.filter(v => v.cardId !== user.cardId);
  selectListId.value = selectListId.value.filter(v => v !== user.cardId);
}
const noticeUserList = (list) => {
  const selectedList = list.reduce((acc, cur) => {
    const user = {
      name: cur.name,
      avatar: cur.avatar,
      cardId: cur.cardId,
      openid: cur.openId
    }
    acc.push(user);
    selectListId.value.push(cur.cardId);
    const isDone = pickListData.value.findIndex(v=>v.openid === cur.openId);
    if(isDone !== -1){
      pickListData.value[isDone]['done'] = true;
    }
    return acc;
  }, [{
    name: selfInfo?.title || '',
    avatar: selfInfo?.avatar || '',
    cardId: selfInfo.openid,
    openid: selfInfo.openid
  }])
  formData.mentionUsers = _.uniqBy([...formData.mentionUsers, ...selectedList], 'cardId');
}
const onSubmit = async (done = false, isChangeTime = false, isSaveToMy = false, needRefresh = true) => {
  const valid = await formRef.value?.validate();
  if (!isSaveToMy && typeof valid !== 'boolean') return;
  let { title, desc, noticeTyp, repeatTyp, mentionUsers, noticeMatters } = formData;
  // 调整knock_at传入逻辑 单次提醒传入具体时间 周期性提醒则传入当前时间
  if (noticeMatters.length && noticeMatters.some(v => v.name === '')) {
    // return MessagePlugin.warning('提醒事项选项为空');
    return;
  }
  if (!title) return MessagePlugin.warning('标题不能为空');
  if(showDatePicker('EVERY_WEEK') && formData.week.length < 1){
    return MessagePlugin.warning('每周提醒频次不能为空');
  }
  let data = {
    title,
    desc,
    noticeTyp,
    repeatTyp: repeatTyp?.length ? repeatTyp : ['NONE'],
    knockAt: format_knock_at(),
    owner: remindDetailData.value?.owner,
    share: remindDetailData.value?.share,
    parent: remindDetailData.value?.parent,
    openid: remindDetailData.value?.openid,
    mentionUsers,
    noticeMatters,
    chooseWeekdays: formData.week,
  }
  if (isSaveToMy) {
    delete data.owner;
    delete data.share;
    delete data.parent;
    delete data.openid;
  }
  if (done) {
    data.done = true
  }
  if (!isSaveToMy && !done && isChangeTime && moment(moment(formData.date).format('YYYY-MM-DD HH:mm')).valueOf() <= moment().valueOf() && formData.noticeTyp == 'ONCE') {
    const confirmDia = DialogPlugin.confirm({
      header: t('account.tip'),
      body: t('zx.remind.pastTimeTip'),
      theme: 'warning',
      confirmBtn: {
        content: t('identity.confirm'),
      },
      onConfirm: async () => {
        confirmDia.destroy();
        await saveRemind(data, done, isSaveToMy, needRefresh);
        isDetail.value = true;
      },
      onClose: () => {
        confirmDia.hide();
      },
    })
  } else {
    // console.log(2222222)
    await saveRemind(data, done, isSaveToMy, needRefresh)
    isDetail.value = true;
  }
}
const saveRemind = async (data, isDone, isSaveToMy, needRefresh) => {
  if(!isSaveToMy && editDeleteRemind.value === 'only'){
    // 修改当次提醒不包括后续提醒时，提醒时间使用表单中的时间
    data.knockAt = new Date(moment(formData.date).format("YYYY-MM-DD HH:mm")).getTime();
  }
  const [err, res] = isSaveToMy ? await to(addNotice(data)) : editDeleteRemind.value === 'only' ? await to(editNotice(data)) : await to(editNoticeAfter({ ...data, action: editDeleteRemind.value === 'follow' ?  1 : 0 }));
  if (err) return;
  zhixingStore.remindDetailDialogData.props.openid = res.data.data.openid;
  if(needRefresh){
    zhixingStore.remindModelRefresh = !zhixingStore.remindModelRefresh//提醒单独模块数据刷新
  }
  if (isSaveToMy) {
    close();
    return;
  }
  remindDetailData.value = { ...remindDetailData.value, ...data };
  isRemindTip.value && MessagePlugin.success(t('zx.remind.editSuccess'));
  isRemindTip.value = true;
  if (isRefresh.value) {
    emits('refresh', true);
    isRefresh.value = false;
  }
}

// 删除提醒
const onConfirmDelete = () => {
  zhixingStore.deleteStatus = editDeleteRemind.value;
  if (isRepeatRemind.value) { // 重复
    if (editDeleteRemind.value === 'only') { // 本次
      deleteNotice(zhixingStore.remindDetailDialogData.props?.openid).then(res => {
        MessagePlugin.success('操作成功')
        close()
      })
    }
    if (['follow', 'all'].includes(editDeleteRemind.value)) {
      // deleteNoticeAfter(zhixingStore.remindDetailDialogData.props?.openid,
      deleteNoticeAfterNew(zhixingStore.remindDetailDialogData.props?.openid,
      { action: editDeleteRemind.value === 'follow' ?  1 : 0 }).then(res => {
        MessagePlugin.success('操作成功')
        zhixingStore.searchRefresh = !zhixingStore.searchRefresh
        zhixingStore.remindModelRefresh = !zhixingStore.remindModelRefresh//提醒单独模块数据刷新
        close()
      })
    }
    zhixingStore.remindModelRefresh = !zhixingStore.remindModelRefresh//提醒单独模块数据刷新
  } else { // 不重复
    deleteNotice(zhixingStore.remindDetailDialogData.props?.openid).then(res => {
      MessagePlugin.success('操作成功')
      zhixingStore.searchRefresh = !zhixingStore.searchRefresh
      zhixingStore.remindModelRefresh = !zhixingStore.remindModelRefresh//提醒单独模块数据刷新
      // close()
      deleteNoticeAfter1();
    })
  }
  console.log('onConfirmDelete', deleteCheckedForm)
  deleteVisible.value = false;
}

// 删除提醒数据以后返回的数据
const deleteNoticeAfter1 = () => {
  zhixingStore.remindDetailDialogData.props = null;
  zhixingStore.remindDetailDialogData.visible = false;
  useChatExtendStore().hideChatDialog();
  emits('delete', remindDetailData.value?.openid);
}

const radioChange = (value) => {
  deleteCheckedForm.onlyThis = false
  deleteCheckedForm.otherAll = false
  deleteCheckedForm[value] = true
}
const _transData = (data) => {
  data.knockAt = Number(data.knockAt);
  isFirstInit.value = true;
  let dataTimeObj = _transData_knock_at(data)
  formData.title = data?.title || ''
  formData.desc = data?.desc || ''
  formData.noticeTyp = data?.noticeTyp || 'ONCE'
  formData[dataTimeObj.label] = dataTimeObj.value
  formData.time = moment(data.knockAt).format('HH:mm')
  formData.dateTime = moment(data.knockAt).format('YYYY-MM-DD HH:mm')
  formData.repeatTyp = (data?.repeatTyp?.length === 1 && data?.repeatTyp?.[0] === 'NONE') ? '' : data?.repeatTyp || ''
  formData.openid = data?.openid || ''
  formData.content = data?.content || ''
  formData.noticeMatters = data.noticeMatters ? data?.noticeMatters.map(v => {
    v['finish'] = !!v.finishUser;
    return v;
  }) : [];
  // console.log(formData, 'formData aaaa')
  formData.mentionUsers = data?.mentionUsers || [];
  formData.chooseWeekdays = data?.chooseWeekdays || [];
  formData.week = data?.chooseWeekdays || [];
  if (data.knockAt) {
    formData.date = moment(data.knockAt).format('YYYY-MM-DD HH:mm');
  }
  // 选中的人
  selectListId.value = formData?.mentionUsers.map(v=> v.openid);
  nextTick(() => {
    isFirstInit.value = false;
  })
}

// 点击跳转消息
const goToIm = () => {
  console.log('remindDetailData', remindDetailData.value)
  if (remindDetailData.value.typ === 'GROUP') {
    openChat({ main: remindDetailData.value.cardId, group: remindDetailData.value.fromId })
  } else {
    openChat({ main: remindDetailData.value.cardId, peer: remindDetailData.value.fromId })
  }
}

// 判断创始人是否是自己
const isCreator = () => {
  return zhixingStore.remindDetailDialogData.props?.owner === getOpenid();
}

watch(() => zhixingStore.remindDetailDialogData.visible, async (val) => {
  if (val) {
    let res = await getNotice(zhixingStore.remindDetailDialogData.props?.openid)
    if (res?.status === 200) {
      zhixingStore.remindDetailDialogData.props = res?.data?.data
      remindDetailData.value = res.data?.data
      _transData(res?.data?.data)
      drawerVisible();
      showFormRef.value = val
    }
  } else {
    showFormRef.value = false
  }
}, { deep: true })

watch(isDetail, async (val) => {
  if(val){
    let res = await getNotice(zhixingStore.remindDetailDialogData.props?.openid)
    if (res?.status === 200) {
      zhixingStore.remindDetailDialogData.props = res?.data?.data
      remindDetailData.value = res.data?.data
      _transData(res?.data?.data)
      drawerVisible();
      showFormRef.value = val
    }
  }
})

let flag = false;
watch(() => props.openid, async (val) => {
  if (val) {
    if (flag) return;
    console.log(2)
    flag = true;
    let res = await getNotice(val)
    flag = false;
    if (res?.status === 200) {
      zhixingStore.remindDetailDialogData.type = 'edit'
      zhixingStore.remindDetailDialogData.props = res?.data?.data
      remindDetailData.value = res.data?.data
      drawerVisible();
      _transData(res?.data?.data)
      showFormRef.value = true
    }
  } else {
    showFormRef.value = false
  }
})

// 判断提醒是否被删除
const isRemindDelete = (time) => {
  if (time === 0) {
    return true;
  } else if (time > 0) {
    return false;
  }
  return true;
}

const drawerVisible = () => {
  if (props.isDialog) {
    zhixingStore.remindDetailDialogData.visible = true;
    zhixingStore.addRemindDialogData.visible = false
    // console.log('visible true')
  }
}

const sessionId = ref('');
const conversation = ref(1);
onMounted(async () => {
  const msgInfo = zhixingStore.addRemindDialogData.props?.fromMsgInfo;
  conversation.value = msgInfo?.conversationType;
  if (props.openid) {
    // console.log(1,props.openid)
    let res = await getNotice(props.openid)
    if (res?.status === 200) {
      zhixingStore.remindDetailDialogData.type = 'edit'
      zhixingStore.remindDetailDialogData.props = res?.data?.data
      remindDetailData.value = res.data?.data
      drawerVisible();
      _transData(res?.data?.data)
      showFormRef.value = true;
      sessionId.value = res.data?.data.fromId;
      conversation.value = res.data?.data?.typ === "GROUP" ? 3 : 1;
      getRecentList(res?.data?.data,sessionId.value || '');
    }
  } else {
    showFormRef.value = false
  }
})

// 获取最近联系人
const getRecentList = async (data?:any,fromId:string = sessionId.value) => {
  try {
    // recentList.value = await selectData.recentList(getImCardIds());
    const list = await selectData.recentList(getImCardIds());
    recentList.value = list.map((v:any)=>{
      const vFind = v.attachment?.member.find((item:any)=>v.openId === item.openId);
      return {
        name:v.name,
        avatar:v.avatar,
        cardId:v.cardId,
        openid: v.openId,
        team_name: vFind?.teamName || '',
        done:false
      }
    });

    if (!fromId) {
      pickList.value = recentList.value;
      pickListData.value = pickList.value;
      // 选中的人
      pickListData.value.forEach((item)=>{
        item.done = selectListId.value.includes(item.openid)
      })
    }else {
      getGroupMemberList(data,fromId);
    }
    pickListData.value = _.uniqBy(pickList.value, 'cardId');
  } catch (error) {
    console.log(error);
  }
}

const getGroupMemberList = (item:any,fromId?:string) =>{
  if (item.typ === "GROUP") {
    if(!item.fromId) return
    getGroupMemberListApi({member:{group:item.fromId}}).then(res=>{
      const array = res?.data?.array;
      const arr = array?.members?.arr;
      if(!arr.length) return;
      if (fromId) {
        // console.log('msgStore.allGroups',msgStore.allGroups);
        // console.log('msgStore.arr',arr);
        pickList.value = arr.map((v:any)=>{
          const card = v.card;
          const group = msgStore.allGroups.find((k:any)=>k.group === v.group);
          return {
            name: card.card_name,
            avatar: card.avatar,
            cardId: card.cardId,
            openid: card.openid,
            team_name: group?.attachment?.teamName || card?.team_name || '',
            done: false
          }
        }).filter((v:any)=>v.openid !== getOpenid()) || [];
      }
      pickListData.value = pickList.value;
      pickListData.value.forEach((item)=>{
        item.done = selectListId.value.includes(item.openid)
      })
    })
  }else if (item.typ === "PAIR" && !item.fromId.includes('assistant8')) {
    getPrivateChatApi(item.owner,item.fromId).then(res=>{
      console.log('单成员',res);
      const data = res?.data;
      if(!data) return;
      const card = data.card;
      const departments = card?.departments;
      pickList.value = [{
          name: card.card_name,
          avatar: card.avatar,
          cardId: card.cardId,
          openid: card.openid,
          team_name: (departments && departments.length && departments[0]?.name) || card?.team_name || '',
          done: false
      }];
      pickListData.value = pickList.value;
      pickListData.value.forEach((item)=>{
        item.done = selectListId.value.includes(item.openid)
      })
    }).catch(err=>{
      console.error('===>getPrivateChatApi1', err);
      // MessagePlugin.error(err.message);
    })
  }
}

// 选择人
const onChangePick = (e,context) => {
  if (pickListData.value.length === 0) return;
  for(const key in pickListData.value) {
    const data = pickListData.value[key];
    const index = formData.mentionUsers.findIndex(v=>v.openid === data.openid && v.cardId === data.cardId);
    if(index !== -1 && !data['done']) {
      formData.mentionUsers.splice(index,1);
      const selectIndex = selectListId.value.findIndex(v=>v === data.openid);
      if(selectIndex !== -1) selectListId.value.splice(selectIndex,1);
    }else if (index === -1 && data['done']) {
      formData.mentionUsers.push(data);
      selectListId.value.push(data.openid);
    }
  }
}

// 搜索
const handleSearch = (value) =>{
  pickListData.value = pickList.value?.filter(v=> v.name.indexOf(value) !== -1) || [];
}

// 将数组转为周一周二等
const frequencyOptionsName = () => {
  const weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
  return formData.chooseWeekdays.map(v=> weekdays[v - 1]).join('、');
}

// 判断选择提醒频次
const checkFrequency = (noticeTyp:string) => {
  const title = frequencyOptions.value.find(v=>v.value === noticeTyp)?.label || '';
  const knockAt = remindDetailData.value?.knockAt;
  if (noticeTyp === 'EVERY_WEEK') {
    return `${title}-${frequencyOptionsName()}`;
  } else if (noticeTyp === 'EVERY_MONTH') {
    return `${title}-${moment(knockAt).format('DD')}日`;
  } else if (noticeTyp === 'EVERY_YEAR') {
    return `${title}-${moment(knockAt).format('MM')}月${moment(knockAt).format('DD')}日`;
  }
  return title;
}

const editDeleteRemind = ref<'follow' | 'only' | 'all'>('only');

// 点击重复编辑按钮
const onRepeatEdit = () => {
  remindState.value = 'edit';
  isEditRemindDialog.value = true;
}

// 确定提醒删除弹窗确定按钮
const onEditRemindConfirm = (edit: 'follow' | 'only' | 'all') => {
  console.log('edit', edit);
  editDeleteRemind.value = edit;
  console.log(editDeleteRemind.value)
  if (remindState.value === 'edit') {
    isDetail.value = false;
  } else {
    onConfirmDelete();
  }
}

// 判断规则
const isJudgmentRules = computed(() => isRepeatRemind.value && remindState.value === 'edit' && editDeleteRemind.value === 'only');

</script>

<style lang="less" scoped>
.width-box {
  // top: 40px !important;

  &.not-from-chat {
    // width: calc(100% - 580px) !important;
    width: 100%;
    height: calc(100% - 40px);
    // top: 40px;
  }
}

.drawerBox {
  position: absolute;
  width: 100%;
  top: 0;
  bottom: 0;
  right: 0;
  background-color: @kyy_white;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  overflow-y: overlay;
}

.drawerHeader {
  height: 64px;
  margin: 0 16px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;

  border-bottom: 1px solid @kyy_gray_3;
  font-size: 14px;

  font-weight: 700;
  text-align: left;
  color: #13161b;
  line-height: 22px;
  flex-shrink: 0;

  .t-button {
    margin-right: 8px;
    // color: var(--color-button-border-kyy-color-button-border-text-default, #516082);
    text-align: center;
    /* kyy_fontSize_2/regular */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
    height: 28px;
  }

  .t-button:nth-child(1) {
    border: 1px solid #4D5EFF;
    background: #EAECFF;
    color: #4D5EFF;
    // width: 60px;
    min-width: 60px;
    height: 28px;
  }

  :deep(.t-button__text) {
    font-weight: normal !important;
  }

  .act-icon-group {
    display: flex;
    align-items: center;

    .icon {
      margin-right: 12px;
      color: #828DA5;
      transform: scale(1.3);
      cursor: pointer;

      &:last-of-type {
        margin-right: 0;
      }
    }
  }

  .title {
    color: var(--kyy_color_modal_title, #1A2139);

    /* kyy_fontSize_3/bold */
    font-family: PingFang SC;
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
    /* 150% */
  }
}

.drawerContent {
  flex: 1;
  padding: 16px;
  padding-top: 12px;
  padding-bottom: 64px;
}

.drawerContent-edit {
  padding: 12px 16px 16px;
}

.drawerFooter {
  padding: 16px;
}

.formDisabledRef .form-item {
  margin-bottom: 0px;

  .form-item-title:nth-child(1) {
    padding-top: 16px;
    // padding-bottom: 4px;
  }

  .form-item-title {
    color: var(--text-kyy-color-text-2, #516082);

    /* kyy_fontSize_2/regular */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
    display: flex;
    align-items: center;
    flex-shrink: 0;

    .icon {
      margin-right: 4px;
    }
  }

  .form-item-label-box {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    margin-bottom: 5px;
  }

  .form-item-time-box {
    display: flex;

    .date-picker {
      //width: 204px;
      width: 50%;
    }

    .time-picker {
      // margin-left: 10px;
      //width: 204px;
      // width: 50%;
      width: 100%;
    }

    .timePickerWidth {
      //width: 418px;
      width: 100%;
      margin-left: 0;
    }

    .form-item-time-box-time-show {
      color: #000;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 157.143% */
    }
  }

  :deep(.t-input, textarea, .t-select, .t-date-picker, .t-time-picker) {
    border: none;
    box-shadow: none;
  }
}

.desc-item {
  padding-top: 10px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ECEFF5;
  .t-textarea{
    :deep(textarea) {
      padding-left: 0;
    }
  }
}

.title-item {
  :deep(.t-input) {
    font-size: 18px;
    font-weight: 600;
    text-align: left;
    color: #13161b;
    line-height: 26px;
    padding-left: 0;
  }

  :deep(.t-input:hover) {
    border:none!important;
  }

  :deep(.t-textarea){

    textarea{
      border: none;
      box-shadow: none;
      font-size: 18px;
      font-weight: 600;
      text-align: left;
      color: #13161b;
      line-height: 26px;
      padding-left: 0;
      resize: none;
    }
  }
}

.card-box {
  margin-top: 10px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  background: #f1f2f5;
  border-radius: 4px;
  padding: 8px 12px;

  .avatar {
    width: 48px;
    height: 48px;
    border-radius: 5px;
    margin-right: 8px;
  }

  .text {
    flex: 1;
    font-size: 14px;

    font-weight: 400;
    text-align: left;
    color: #13161b;
    line-height: 22px;
    word-break: break-all;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
  }
}

.formDisabledRef {
  .date-picker {
    max-width: 30%;
  }

  :deep(.time-picker .t-input__suffix-icon) {
    display: none;
  }

  .t-input.t-is-disabled {
    background-color: transparent;
  }
}

.f-align {
  display: flex;
  align-items: center;
}

.remind-item {
  padding: 12px 16px;
  border-radius: 8px;
  border: 1px solid var(--border-kyy-color-border-default, #D5DBE4);
  margin-left: 23px;
  margin-top: 12px;

  .split {
    width: 100%;
    height: 1px;
    background: var(--divider-kyy-color-divider-light, #ECEFF5);
    // margin: 12px 0;
    margin: 8px 0;
  }

  .user-name {
    color: var(--text-kyy-color-text-1, #1A2139);

    /* kyy_fontSize_2/regular */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
    margin: 0 4px;
  }

  .finish-time {
    color: var(--text-kyy-color-text-3, #828DA5);

    /* kyy_fontSize_2/regular */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
  }

  .avatar-icon {
    :deep(.t-avatar--round) {
      border-radius: 100% !important;
      // font-size: 14px !important;
      line-height: 20px;
    }
  }
}

.user-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-right: 12px;
  margin-top: 8px;
  position: relative;

  .user-name {
    color: var(--text-kyy-color-text-2, #516082);

    /* kyy_fontSize_1/regular */
    font-family: PingFang SC;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    /* 166.667% */
    width: 48px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    text-align: center;
  }

  .icon {
    width: 16px;
    height: 16px;
    position: absolute;
    right: 4px;
    bottom: 18px;
    cursor: pointer;
    z-index: 10;
  }

  .avatar-icon {
    :deep(.t-avatar--round) {
      border-radius: 100% !important;
      font-size: 14px !important;
      // line-height: 20px;
    }
  }
}

.edit-container {
  top: 0 !important;
  height: 100% !important;
  overflow-y: overflow-y;

  .form-item {
    margin-bottom: 12px;

    :deep(.t-form__controls-content) {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
    }

    .form-item-label-box {
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
      margin-bottom: 5px;
    }

    .form-item-time-box {
      display: flex;
      width: 100%;

      .date-picker {
        //width: 204px;
        width: calc((100% - 10px) / 2);
      }

      .time-picker {
        // margin-left: 10px;
        //width: 204px;
        // width: calc((100% - 10px) / 2);
        width: 100%;
      }

      .timePickerWidth {
        //width: 418px;
        width: 100%;
        margin-left: 0;
      }
    }
  }

  .card-box {
    cursor: pointer;
    margin-top: 10px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    background: #f1f2f5;
    border-radius: 4px;
    padding: 8px 12px;

    .avatar {
      width: 48px;
      height: 48px;
      border-radius: 5px;
      margin-right: 8px;
    }

    .text {
      flex: 1;
      font-size: 14px;

      font-weight: 400;
      text-align: left;
      color: #13161b;
      line-height: 22px;
      word-break: break-all;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
    }
  }

  // :deep(.t-textarea__inner) {
  //   border: none;
  //   padding: 0;
  //   &:focus {
  //     box-shadow: none;
  //   }
  // }
  :deep(.t-textarea__info_wrapper) {
    display: none !important;
  }

  .add-addpend {
    width: 20px;
    height: 20px;
    cursor: pointer;
  }

  .f-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .avatar-icon {
    :deep(.t-avatar--round) {
      border-radius: 100% !important;
      font-size: 14px !important;
    }
  }

  .user-container {
    display: flex;
    flex-wrap: wrap;
  }

  .user-container-edit {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 12px;
    width: 100%;
    // height: 42px;
    padding: 5px 12px;
    margin-top: 16px;
    align-self: stretch;
    border-radius: 4px;
    border: 1px solid var(--select-kyy_color_select_border_loseFocus, #D5DBE4);

    .user-container-left {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    flex: 1;
    // height: 32px;

    .container-left-list {
      display: flex;
      align-items: center;
      padding: 2px 8px;
      gap: 4px;
      height: 32px;
      border-radius: 4px;
      background: var(--kyy_color_tag_bg_gray, #ECEFF5);

      .container-left-list-name {
        color: var(--kyy_color_tag_text_black, #1A2139);
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
        max-width: 69px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .icon-delete {
        box-sizing: border-box;
        font-size: 20px;
        cursor: pointer;
        color: #828DA5;
      }
    }

    .container-left-add {
      flex: 1;
      display: flex;
      align-items: center;
      cursor: pointer;
      min-width: 84px;
      height: 32px;
      color: var(--text-kyy_color_text_5, #ACB3C0);
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */

      :deep(.t-popup) {

        .t-popup__content {
          padding: 8px;
        }
      }
    }
  }

    .user-container-popup {
      width: 325px;

    .user-container-popup-content {
      padding: 12px 0 0;

      .user-container-popup-content-name {
        color: var(--text-kyy_color_text_3, #828DA5);
        margin-bottom: 4px;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px; /* 166.667% */
      }

      .user-container-popup-content-box {
        display: flex;
        flex-direction: column;
        gap: 2px;
        max-height: 220px;
        overflow-y: scroll;

        .user-container-popup-box-list {
          display: flex;
          align-items: center;
          gap: 8px;
          height: 48px;
          padding: 8px 0 8px 8px;

          &:hover {
            border-radius: 8px;
            background: var(--bg-kyy_color_bg_list_hover, #F3F6FA);
          }

          .user-container-popup-box-list-left {
            width: 20px;
            display: flex;
            // align-items: center;
            // justify-content: center;
          }

          .user-container-popup-box-list-right {
            display: flex;
            align-items: center;
            gap: 8px;

            .icon-box-list-right {
              width: 32px;
              height: 32px;
              border-radius: 50%;
            }

            .popup-box-list-right-title {
              display: flex;
              flex-direction: column;

              .popup-box-list-right-title-name {
                display: flex;
                color: var(--text-kyy_color_text_1, #1A2139);
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 22px; /* 157.143% */
                gap: 4px;

                span {
                  display: flex;
                  height: 20px;
                  min-height: 20px;
                  max-height: 20px;
                  padding: 2px 4px;
                  justify-content: center;
                  align-items: center;
                  gap: 4px;
                  border-radius: var(--kyy_radius_tag_s, 4px);
                  background: var(--kyy_color_tag_bg_brand, #EAECFF);
                  color: var(--kyy_color_tag_text_brand, #4D5EFF);
                  text-align: center;
                  font-size: 12px;
                  font-style: normal;
                  font-weight: 400;
                  line-height: 20px; /* 166.667% */
                }
              }

              i {
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 1;
                overflow: hidden;
                color: var(--kyy_blue-kyy_color_kyyBlue_hover, #49BBFB);
                font-feature-settings: 'clig' off, 'liga' off;
                text-overflow: ellipsis;
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: 20px; /* 166.667% */
                font-style: normal;
              }
            }
          }
        }
      }

      .popup-more{
        display: flex;
        flex-direction: column;
        align-items: center;
        color: var(--text-kyy_color_text_2, #516082);
        font-size: 14px;
        line-height: 26px;
      }
    }
  }

  .user-container-line {
    display: flex;
    height: 16px;
    min-width: 1px;
    max-width: 1px;
    align-items: flex-start;
    // gap: 4px;
    background: var(--divider-kyy_color_divider_light, #ECEFF5);
  }

  .user-container-right {
    display: flex;
    width: 24px;
    height: 24px;
    padding: 2px;
    box-sizing: border-box;
    justify-content: center;
    align-items: center;
  }
  }

  .user-info {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-right: 12px;
    margin-top: 16px;
    position: relative;

    .user-name {
      color: var(--text-kyy-color-text-2, #516082);

      /* kyy_fontSize_1/regular */
      font-family: PingFang SC;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      /* 166.667% */
      width: 48px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      text-align: center;
    }

    .icon {
      width: 18px;
      height: 18px;
      position: absolute;
      right: -6px;
      top: -8px;
      cursor: pointer;
      z-index: 10;
    }
  }

  .group-info {
    padding: 2px 8px;
    border-radius: 4px;
    background: var(--kyy_color_tag_bg_gray, #ECEFF5);
    color: var(--kyy_color_tag_text_black, #1A2139);

    /* kyy_fontSize_2/regular */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
    margin-top: 12px;
    display: flex;
    align-items: center;

    .icon {
      margin: 0 8px;
      color: #828DA5;
      transform: scale(1.5);
      cursor: pointer;
    }
  }

  .drawerFooter {
    position: fixed;
    bottom: 0;
    right: 0;
    z-index: 11;
    background-color: #fff;
    padding: 16px;
    text-align: right;
    width: 100%;
    height: 64px;
  }

  .drawerHeader {
    border-bottom: none;
  }
}

.drawerFrom {
  width: 100%;
  background: var(--kyy_color_alert_bg_bule, #EAECFF);
  display: flex;
  padding: 8px 24px;
  align-items: center;
  color: #000;

  .drawerFrom_name {
    min-width: 65px;
    color: var(--color-button-text-brand-kyy-color-button-text-brand-font-default, #4D5EFF);
    text-align: center;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
    padding-left: 8px;
    cursor: pointer;
  }
}

:global(.t-drawer__content-wrapper) {
  overflow: auto !important;
}

.delete-remind-text{
  padding-left: 8px;
  margin-top: 8px;
  color: var(--kyy_color_tag_text_black, #1A2139);
  font-family: PingFang SC;
}

:deep(.week-picker){
  .t-input{
    padding-top: 4px;
    padding-left: 8px;
  }
}

:deep(.t-textarea__info_wrapper_align){
  display: none;
}
</style>
