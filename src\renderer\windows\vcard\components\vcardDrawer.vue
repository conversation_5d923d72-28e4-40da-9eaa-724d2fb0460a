<template>
  <div class="vcard-drawer" id="vcard-drawer-item" :class="inWin?'in-win-vcard-drawer':'in-win-vcard-drawer'"
    v-if="showRemark">
    <div class="subbody">
      <div class="header">
        <span class="content_text">{{headerTitle}}</span>
        <iconpark-icon name="iconerror-a961a3n0" class="btn-close" @click="onClose" />
      </div>
      <div class="vcard-list" style="width: 100%;" :class="inWin?'':'in-win'" v-if="headerTitle==='名片详情'">
        <MyVCardType ref="myVCardTypeRef" :infoData="infoData"></MyVCardType>
        <div class="my-card-btns">
          <!-- trigger="hover" -->
          <t-popup trigger="hover" :disabled="infoData?.phone.length===0" placement="bottom"
            overlay-class-name="vcard-popup">
            <div class="btn-box" :style="{
              width: openid===infoData?.openid?'77px':'60px',
            }" @click="changeBtn('联系电话')">
              <iconpark-icon class="icon32" v-if="infoData?.phone.length>0" name="tel"></iconpark-icon>
              <t-tooltip v-else :content="t('vcard.wtxlxdh')">
                <iconpark-icon class="icon32" name="tel2"></iconpark-icon>
              </t-tooltip>
              <div class="btn-text"> {{t('vcard.lxdh')}}</div>
            </div>
            <template #content>
              <div class="tel-icon" v-for="(item , index) in infoData.phone" :key="index"
                @click="copyTel(`+${item.code} ${item.phone}`)">
                <span>+{{item.code}} {{item.phone}}</span><iconpark-icon class="tel-iconpark"
                  name="iconcopy"></iconpark-icon>
              </div>
            </template>
          </t-popup>
          <div class="lin"></div>
          <t-popup trigger="click" :disabled="!infoData?.address" placement="bottom" overlay-class-name="vcard-popup">
            <div class="btn-box" :style="{
              width: openid===infoData?.openid?'77px':'60px',
            }" @click="changeBtn('位置')">
              <iconpark-icon class="icon32" v-if="infoData?.address&&infoData?.address.length>0"
                name="position"></iconpark-icon>
              <t-tooltip v-else :content="t('vcard.wtxwz')">
                <iconpark-icon class="icon32" name="add2"></iconpark-icon>
              </t-tooltip>
              <div class="btn-text">{{t('vcard.wz')}}</div>
            </div>
            <template #content>
              <div class="address-icon" v-for="(item , index) in infoData?.address" :key="index"
                @click.stop="copyTel(`${item?.title}${item?.desc}${item?.number||''}`)">
                <div>
                  <div v-if="item?.title" class="address-span-title">{{item?.title}}</div>
                  <div v-if="item?.desc" class="address-span-desc">{{item?.desc}}</div>
                  <div v-if="item?.number" class="address-span-number">{{item?.number}}</div>
                </div>
                <iconpark-icon class="tel-iconpark" @click.stop="showAddress(item)"
                  name="iconpositioning"></iconpark-icon>
                <iconpark-icon style="margin-left: 8px;"
                  @c.stoplick="copyTel(`${item?.title}${item?.desc}${item?.number||''}`)" class="tel-iconpark"
                  name="iconcopy"></iconpark-icon>
              </div>
            </template>
          </t-popup>
          <div class="lin"></div>
          <t-popup trigger="hover" :disabled="!infoData.wx_id&&infoData?.email.length===0" placement="bottom"
            overlay-class-name="vcard-popup">
            <div class="btn-box" :style="{
              width: openid===infoData?.openid?'77px':'60px',
            }" @click="changeBtn('社交账号')">
              <iconpark-icon class="icon32" v-if="infoData.wx_id||infoData?.email.length>0"
                name="socialcontact"></iconpark-icon>
              <t-tooltip v-else :content="t('vcard.sjzh')">
                <iconpark-icon class="icon32" name="socialcontact2"></iconpark-icon>
              </t-tooltip>
              <div class="btn-text">{{t('vcard.sjzh')}}</div>
            </div>
            <template #content>
              <div class="sj-icon" v-if="infoData.wx_id" @click="copyTel(infoData.wx_id)">
                <div style="display: flex;">
                  <iconpark-icon style="margin-right: 4px;" class="tel-iconpark" name="wechat"></iconpark-icon>
                  <span class="sj-span">{{infoData.wx_id}}</span>
                </div>
                <iconpark-icon class="tel-iconpark" name="iconcopy"></iconpark-icon>
              </div>
              <div class="sj-icon" v-for="(item , index) in infoData.email" @click="copyTel(item)">
                <div style="display: flex;">
                  <iconpark-icon style="margin-right: 4px;" class="tel-iconpark" name="mail"></iconpark-icon>
                  <span class="sj-span">{{item}}</span>
                </div>
                <iconpark-icon class="tel-iconpark" name="iconcopy"></iconpark-icon>
              </div>
            </template>
          </t-popup>

          <div class="lin"></div>
          <div class="btn-box" :style="{
              width: getOpenid()===infoData?.openid?'77px':'60px',
            }" @click="getIdentityList(infoData?.card_id)">
            <iconpark-icon v-if="infoData?.card_id" class="icon32"
              name="ringkol"></iconpark-icon>
            <iconpark-icon v-else class="icon32" name="ringkol2-fcogpp9e"></iconpark-icon>
            <div class="btn-text"> {{t('vcard.jlk')}}</div>
          </div>
          <div v-if="openid!==infoData?.openid" class="lin"></div>
          <div v-if="openid!==infoData?.openid" :style="{
            width: openid===infoData?.openid?'77px':'60px',
          }" class="btn-box" @click="changeBtn('备注')">
            <iconpark-icon class="icon32" name="remarks2"></iconpark-icon>
            <div class="btn-text">{{t('vcard.bz')}}</div>
          </div>
        </div>
        <div class="company-info select-text" v-if="infoData&&infoData?.teamList.length">
          <div class="company-info-header-box">
            <div class="company-info-header-title">{{t('vcard.gsxx')}}</div>
            <div class="view-all-box" v-if="infoData?.teamList.length>3">
              <span class="view-all" @click="visible=true,dialogTitle='公司信息'">
                全部
              </span>
              <iconpark-icon style="font-size: 20px;" name="iconarrowright"></iconpark-icon>
            </div>
          </div>
          <div class="company-info-list">
            <div class="company-info-item" v-for="(item , index) in infoData?.teamList.slice(0,3)" :key="index"
              @click="goSquare(item)">
              <img v-if="item.logo" class="company-info-item-img" :src="item.logo">
              <iconpark-icon v-else class="company-info-item-img" name="iconorgdefaultavatar"></iconpark-icon>
              <div class="company-info-item-content">
                <div class="company-info-item-name">{{item.name}}
                  <span :class="item.auth===1?'tag-rz':'tag-not-rz'">{{item.auth===1?'已认证':'未认证'}}</span>
                </div>
                <div class="flex-a">
                  <div class="company-info-item-tag">{{item.position.join('|')}}</div>
                  <div class='flexbox-a' :class="item.square_id?'':'haveid'">
                    <div class="square-tag">
                      广场号
                    </div>
                    <iconpark-icon v-if="item.square_id" style="font-size: 24px;color:#4D5EFF"
                      name="iconarrowright"></iconpark-icon>
                  </div>
                </div>
              </div>
              <!-- <img v-if="item.flag" class="company-info-item-img-lk" src="@renderer/assets/rzbs.png"> -->
            </div>
          </div>
        </div>
        <div class="company-info" v-if="infoData&&infoData?.associationList.length">
          <div class="company-info-header-box">
            <div class="company-info-header-title">社群信息</div>
            <div class="view-all-box" v-if="infoData?.associationList.length>3">
              <span class="view-all" @click="visible=true,dialogTitle='社群信息'">
                全部
              </span>
              <iconpark-icon style="font-size: 20px;" name="iconarrowright"></iconpark-icon>
            </div>
          </div>
          <div class="company-info-list">
            <div class="company-info-item" v-for="(item , index) in infoData?.associationList.slice(0,3)" :key="index"
              @click="goSquare(item)">
              <img v-if="item.logo" class="company-info-item-img" :src="item.logo">
              <iconpark-icon v-else class="company-info-item-img" name="iconorgdefaultavatar"></iconpark-icon>
              <div class="company-info-item-content">
                <div class="company-info-item-name">{{item.name}}
                  <span :class="item.auth===1?'tag-rz':'tag-not-rz'">{{item.auth===1?'已认证':'未认证'}}</span>
                </div>
                <div class="flex-a">
                  <div class="company-info-item-tag">{{item.position.join('|')}}</div>
                  <div class='flexbox-a' :class="item.square_id?'':'haveid'">
                    <div class="square-tag">
                      广场号
                    </div>
                    <iconpark-icon v-if="item.square_id" style="font-size: 24px;color:#4D5EFF"
                      name="iconarrowright"></iconpark-icon>
                  </div>
                </div>
              </div>
              <!-- <img v-if="item.flag" class="company-info-item-img-lk" src="@renderer/assets/rzbs.png"> -->
            </div>
          </div>
        </div>
        <div class="company-info" v-if="infoData?.attachment&&infoData?.attachment.length">
          <div class="company-info-header-box">
            <div class="company-info-header-title"> {{t('vcard.mpfj')}}</div>
            <!-- <div class="view-all-box" v-if="infoData?.attachment.length>8">
              <span class="view-all" @click="visible=true,dialogTitle='名片附件'">
                {{t('vcard.viewall')}}
              </span>
              <iconpark-icon style="font-size: 20px;" name="iconarrowright"></iconpark-icon>
            </div> -->
          </div>
          <div class="file-info-list">
            <div v-for="(item , index) in infoData.attachment" :key="index" class="file-info-list-item">
              <img v-if="imgType.includes(item.file_type.toLowerCase())" :src="item.file_url" @click="viewFile(item)"
                class="file-item-video">
              <div v-else style="position: relative; flex: 1;" @click="viewFile(item)">
                <img class="file-item-video specialVideo"
                  :src="item.file_url+'?x-oss-process=video/snapshot,t_1000,f_jpg,w_0,h_0,m_fast,ar_auto'" />
                <img class="play-icon" src="@/assets/playvideo.svg">
              </div>
            </div>
          </div>
        </div>
        <div class="company-info" v-if="infoData?.hobbies.length">
          <div class="company-info-header-box">
            <div class="company-info-header-title">{{t('vcard.xqah')}}</div>
          </div>
          <div class="tag-list">
            <div class="tag-item" v-for="(item , index) in infoData.hobbies" :key="index">
              {{item}}
            </div>
          </div>

        </div>
      </div>
    </div>

    <div class="del-vcard">
      <t-button style="width: 88px;" theme="primary" variant="outline" @click="delVcard"
        v-if="infoData.collected&&infoData?.openid!==getOpenid()">
        {{t('vcard.scmp')}}
      </t-button>
      <t-button v-if="infoData?.relay===1||infoData?.openid===getOpenid()" style="width: 88px;" theme="primary" variant="outline" @click="forwardCard">
        转发名片
      </t-button>
      <t-button @click="subVcard" v-if="infoData?.openid&&!infoData.collected&&infoData?.openid!==getOpenid()"
      style="width: 100%;" theme="primary">
         <!-- style="width: 328px;" -->
        保存名片
      </t-button>
    </div>
    <t-drawer v-model:visible="visible" :zIndex="dialogTitle==='新增备注'||dialogTitle==='编辑备注'?15011:1500" :header="null"
      :closeBtn="false" size="86%" class="in-im-vcard-drawer" :footer="false" @close="closeFn"
      :class="inWin?'in-im-vcard-addbz':''" placement="bottom">
      <div style="height: 555px;overflow-y: auto;overflow-x: hidden; " class="changBtnDialogbody">
        <div class="subbody">
          <div class="header-wrap">
            <span class="header-title">{{dialogTitle}}</span>
            <iconpark-icon name="iconerror-a961a3n0" class="btn-close" @click="closeFn" />
          </div>

          <div class="identity-info-list" v-if="dialogTitle==='选择身份'">
            <div class="identity-item" v-for="(item , index) in identityList" :key="card_id"
              @click="changeIdentity(item)">
              <avatar avatarSize="44px" style="margin-right: 4px;" :image-url="item.avatar ?? ''" :user-name="item.name"
                :round-radius="true" />
              <div style="flex: 1;">
                <div class="identity-item-name">{{item.name}}</div>
                <div class="identity-item-desc" :style="{color: item.team_name==='个人' ? '#62BF7C' : '#FC7C14'}">
                  {{item.team_name}}</div>
              </div>
              <iconpark-icon name="iconarrowright" class="identity-item-right"></iconpark-icon>
            </div>
          </div>
          <div class="company-info-list" v-if="dialogTitle==='公司信息'||dialogTitle==='社群信息'">
            <div class="company-info-item"
              v-for="(item , index) in dialogTitle==='公司信息'?infoData.teamList:infoData.associationList" :key="index"
              @click="goSquare(item, dialogTitle==='公司信息'||dialogTitle==='社群信息')">
              <img v-if="item.logo" class="company-info-item-img" :src="item.logo">
              <iconpark-icon v-else class="company-info-item-img" name="iconorgdefaultavatar"></iconpark-icon>
              <div class="company-info-item-content">
                <div class="company-info-item-name">{{item.name}}
                  <span :class="item.auth===1?'tag-rz':'tag-not-rz'">{{item.auth===1?'已认证':'未认证'}}</span>

                </div>
                <div class="company-info-item-tag">
                  <!-- <div class="position"> {{item.position.join('|')}}</div> -->
                  <div class="flex-a">
                    <div class="company-info-item-tag">{{item.position.join('|')}}</div>
                    <div class='flexbox-a' :class="item.square_id?'':'haveid'">
                      <div class="square-tag">
                        广场号
                      </div>
                      <iconpark-icon v-if="item.square_id" style="font-size: 24px;color:#4D5EFF"
                        name="iconarrowright"></iconpark-icon>
                    </div>
                  </div>
                </div>
              </div>
              <!-- <img v-if="item.flag" class="company-info-item-img-lk" src="@renderer/assets/rzbs.png"> -->
            </div>
          </div>
          <div class="company-info-list" v-if="dialogTitle==='名片附件'">
            <div class="file-list">
              <div class="flie-item" v-for="(item , index) in infoData?.attachment" :key="index">
                <img v-if="imgType.includes(item.file_type.toLowerCase())" :src="item.file_url" @click="viewFile(item)"
                  class="file-item-video">
                <div style="position: relative; width: 100%; flex: 1;" @click="viewFile(item)" v-else>
                  <img class="file-item-video"
                    :src="item.file_url+'?x-oss-process=video/snapshot,t_1000,f_jpg,w_0,h_0,m_fast,ar_auto'" />
                  <!-- <iconpark-icon name="icon-play" class="play-icon"></iconpark-icon> -->
                  <img class="play-icon" src="@/assets/playvideo.svg">

                </div>
              </div>
            </div>
          </div>
          <div class="company-info-list" v-if="dialogTitle==='新增备注'||dialogTitle==='编辑备注'" style="padding: 0 12px;">
            <t-textarea class="rounded-[4px]" style="max-height: calc(100vh - 250px);" placeholder="请输入备注信息"
              v-model="remarkData" />
            <div class="bz">
              {{t('vcard.kyybetips')}}
            </div>
            <div class="footer-box-add" style="justify-content: end;gap: 8px;padding-right: 12px;">
              <t-button theme="default" variant="outline" @click="remarkData='',visible=false" style="width: 80px;">
                取消
              </t-button>
              <t-button theme="primary" :disabled="!remarkData" @click="addRemarkSub" style="width: 80px;">
                保存
              </t-button>

            </div>
          </div>
        </div>
      </div>
    </t-drawer>
    <t-drawer v-model:visible="visiblebz" :header="false" :closeBtn="false" size="100%"
      :class="inWin?'in-im-vcard-drawer-right-bz':'aaaaaaaaaaaaaaaaa'" :footer="false" @close="closeFn"
      placement="right">
      <div style="height: 555px;overflow-y: auto;overflow-x: hidden; " class="remark-body">
        <div class="subbody">
          <div class="header-wrap">
            <span class="header-title">{{dialogTitle==='新增备注'||dialogTitle==='编辑备注'?'备注':dialogTitle}}</span>
            <iconpark-icon name="iconerror-a961a3n0" class="btn-close" @click="visiblebz = false" />
          </div>
          <div class="vcard-list">
            <div class="content-data">
              <div class="content-item-remark" v-for="(item, index) in remarkListArr" :key="index">
                <div class="content-item-title-remark">{{item.content}}</div>
                <div class="content-item-content-remark">
                  <div>{{dayjs(item.updated_at*1000).format("YYYY-MM-DD HH:mm")}}</div>
                  <div style="display: flex;align-items: center;gap: 4px;">
                    <div class="icon-box" @click="editRemark(item)">
                      <iconpark-icon class="remark-icon" name="iconedit"></iconpark-icon>
                    </div>
                    <div class="icon-box" @click="item.visiblePopconfirm = true">
                      <t-popconfirm :popup-props="{overlayClassName: 'vcard-popconfirm-popup'}" placement="left"
                        :showArrow="false" :visible="item.visiblePopconfirm" theme="default"
                        :content="t('vcard.delbz')">
                        <iconpark-icon class="remark-icon" name="icondelete"></iconpark-icon>
                        <template #cancelBtn>
                          <t-button size="small" theme="default" variant="outline" style="margin-right: 12px"
                            @click="item.visiblePopconfirm = false,delloading=false">取消</t-button>
                        </template>
                        <template #confirmBtn>
                          <t-button size="small" :disabled="delloading" :loading="delloading" theme="primary"
                            @click="delFn(item)">确定</t-button>
                        </template>
                      </t-popconfirm>
                    </div>
                  </div>
                </div>
              </div>
              <noData :tip="t('vcard.zwbz')" center v-if="remarkListArr?.length===0"> </noData>
              <div class="footer-box" v-if="!visible">
                <t-button theme="primary" @click="addRemark" style="width: 352px;">
                  <template #icon><add-icon /></template>
                  新增备注
                </t-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </t-drawer>
    <apply-dialog v-model:visible="applyVisible" :cardInfo="cardInfo" :myId="cardInfo?.myId" @onconfirm="onconfirm" />
    <!-- <t-dialog v-model:visible="visible" @close="closeFn" :show-in-attached-element="true" :header="dialogTitle"
      :footer="false" dialogClassName="changBtnDialog">

    </t-dialog> -->
  </div>
</template>
<script setup lang="ts">
  import MyVCardType from "@/windows/vcard/components/MyVCardType.vue"
  import { ref, onMounted, computed, watch, nextTick, onUnmounted, onActivated } from "vue";
  import { vCardDetail, folderCollect, remarkList, createRemark, modifyRemark, delRemarkCard, delRemoveCard, getIdentityListData } from "@renderer/api/vcard/index";
  import useClipboard from "vue-clipboard3";
  import { useI18n } from "vue-i18n";
  import { openExternalMap } from '@renderer/components/common/map/utils';
  import { DialogPlugin, MessagePlugin, LoadingPlugin } from "tdesign-vue-next";
  import noData from "@renderer/components/common/Empty.vue";
  import dayjs from "dayjs";
  import { imgType, fileTypes } from "@/views/zhixing/constant";
  import BottomTopPop from "./BottomTopPop.vue";
  import { goSquareModule } from "@/views/square/utils/business";
  import avatar from "@renderer/components/kyy-avatar/index.vue";
  import applyDialog from '@renderer/views/identitycard/dialog/applyContact.vue';
  import { cardIdType, combMsgInfo, cardRelation } from "@/views/identitycard/data";
  import { checkApply } from "@/api/contacts/api/friend";

  import { openChat } from "@/utils/share";
  import { createMsg } from "@/api/identity/api/card";
  import {
    getOpenid
  } from "@renderer/utils/auth";
  import LynkerSDK from '@renderer/_jssdk';

  const { ipcRenderer, shell } = LynkerSDK;
  // const headerTitle = ref('名片详情')
  //
  const porps = defineProps({
    inWin: {
      type: Boolean,
      default: false,
    },

  });

  const openid = window.localStorage.getItem('openid')
  const remarkData = ref('')
  const headerTitle = ref('名片详情')
  const delloading = ref(false);
  const { t } = useI18n();
  const { toClipboard } = useClipboard();
  const cardType = ref(1)
  const infoData = ref({
    phone: [],
    email: [],
    openid: '',
    uuid: '',
    associationList: [],
    teamList: [], hobbies: [],
    attachment: []
  })
  const cardInfo = ref({});
  const applyVisible = ref(false);

  const dialogTitle = ref('公司信息')
  const visible = ref(false)
  const delitem = ref(null);
  const rela = ref(null);
  const identityList = ref([])
  const changeIdentity = async (item) => {
    cardInfo.value.applyCardId = infoData.value.card_id;//对方身份卡
    cardInfo.value.avatar = infoData.value.avatar;
    cardInfo.value.name = infoData.value.name;
    cardInfo.value.teamName = infoData.value?.show_team?.name;//对方身份卡
    cardInfo.value.cardId = infoData.value.card_id;//对方身份卡
    cardInfo.value.openid = infoData.value.openid;//对方openid
    cardInfo.value.myId = item.card_id;//自己身份卡
        let reverseRes = await checkApply({
      cardIdOne: cardInfo.value.applyCardId,
      cardIdTwo: cardInfo.value.myId
    });
      if (reverseRes.data.data?.is_apply) {
      MessagePlugin.error('待通过');
      return;
    }
    rela.value = await cardRelation(cardInfo.value.applyCardId, cardInfo.value.myId);
    console.log(rela.value, 'relarelarela');
    
    if (rela.value === 'FRIEND'||rela.value ==='BUSINESS') {
      visible.value = false
      openChat({ main: cardInfo.value.myId, peer: cardInfo.value.applyCardId });
    } else {
      visible.value = false

      applyVisible.value = true
    }
    // let reverseRes = await checkApply({
    //   cardIdOne: cardInfo.value.myId,
    //   cardIdTwo: cardInfo.value.cardId
    // });
    // console.log(reverseRes,'reverseResreverseRes');

    // if (reverseRes.data.data?.is_apply) {
    // //   MessagePlugin.error('禁止与自己建立聊天');myId
    // //   return;
    // }

    console.log(item, infoData.value, rela.value, 'itemmmmmmmmmmm');
    console.log(rela.value, 'itemmm2323232mmmmmmmm');
  }
  const onconfirm = async () => {
    console.log(cardInfo.value, 'cardInfo.valuecardInfo.value');
    let origin = cardIdType(cardInfo.value.cardId) === 'outer' || cardIdType(cardInfo.value.myId) === 'outer' ? 'BUSINESS' : 'FRIEND';
    const params = await combMsgInfo(cardInfo.value.applyCardId, cardInfo.value.myId, origin);
    createMsg(params).then(() => {
      openChat({ main: cardInfo.value.myId, peer: cardInfo.value.applyCardId });
    }).catch(err => {
      MessagePlugin.error(err.response.data?.message || '添加联系人失败');
    });
  }
  const getIdentityList = (val) => {
    console.log(getOpenid(), infoData?.value?.openid, 'openid.value, infoData?.value?.openid');

    if (getOpenid() === infoData?.value?.openid) {
      return MessagePlugin.error('禁止与自己建立聊天');
    }
    if (val) {
      getIdentityListData().then(res => {
        console.log(res, 'res.data.data.list');
        identityList.value = res.data.data.list
        visible.value = true
      })
      dialogTitle.value = '选择身份'
    }
  }
  const goSquare = (item, isJump = true) => {
    const { square_id } = item || {};
    if (!isJump) {
      return;
    }
    if (!square_id) {
      MessagePlugin.info('公司暂未开通广场号');
      return;
    }
    const query = { redirect: '/square/info', id: square_id, needShowDialog: true };

    goSquareModule(query, { toOrg: true });
  }
  const openIdCard = () => {
    const { openid } = infoData.value;
    if (!openid) {
      console.error('not found openid in infoData!')
      return;
    }
    ipcRenderer.invoke("identity-card", {
      cardId: openid,
      myId: getOpenid(),
      showMoreCard: "showMoreCard"
    });
  }

  const showRemark = ref(true)
  const emits = defineEmits(["close-drawer"]);
  let masData = null
  const editData = ref(null)
  const viewFile = (item) => {
    console.log(item, 'itemmmmmmmmmmm');
    let objs = [{
      type: item.file_type === 'video' ? 'mp4' : item.file_type,
      url: item.file_url,
    }]
    ipcRenderer.invoke('preview-file', JSON.stringify(objs));
  }
  const delVcard = () => {

    delRemoveCard(infoData.value.id).then(res => {
      MessagePlugin.success({
        content: '删除成功',
        duration: 2000,
        offset: ['0', '-60']
      });
      // getRemarkList();
      onClose()
    }).catch(err => {
      MessagePlugin.error(err.response.data.message);
    })
  }
  const visiblebz = ref(false)
  const delFn = (item) => {
    console.log(item, 'itemmmmmmmmmmmm');

    // delitem.value = item
    delloading.value = true
    delRemarkCard(item.id).then(res => {
      MessagePlugin.success('删除成功');
      getRemarkList();
    }).catch(err => {
      MessagePlugin.error(err.response.data.message);
    }).finally(() => {

      console.log('触发这里喔');

      delloading.value = false;
    })

    // visible = false
  }

  const myVCardTypeRef = ref(null);
  const forwardCard = async () => {
    const cardImg = await myVCardTypeRef.value?.getUrl();
    ipcRenderer.invoke("show-main");
    ipcRenderer.invoke("set-popbv",
      {
        show: true, type: 'dialogVcard', data: {
          val: JSON.stringify({
            ...infoData.value,
            cardImg
          })
        }
      }
    );
  }

  const editRemark = (item) => {
    visible.value = true;
    dialogTitle.value = '编辑备注'
    editData.value = item
    remarkData.value = item.content
  }

  const copyTel = (text) => {
    toClipboard(text);
    MessagePlugin.success(t("order.czcg"));
  }
  const showAddress = (data) => {
    openExternalMap({ lng: data.lng, lat: data.lat, name: data.title });
  }
  const closeFn = () => {
    remarkData.value = ''
    visible.value = false
  }
  const addRemarkSub = () => {
    if (dialogTitle.value === '新增备注') {
      createRemark({
        card_id: infoData.value.id,
        content: remarkData.value
      }).then(res => {
        MessagePlugin.success('新增成功');
        remarkData.value = ''
        getRemarkList()
        setInfo(infoData.value)
        visible.value = false
      }).catch(err => {
        console.log(err, '啊啊打算的');

        MessagePlugin.error(err.response.data.message);
      })
    }
    if (dialogTitle.value === '编辑备注') {
      modifyRemark({
        remark_id: editData.value.id,
        content: remarkData.value
      }).then(res => {
        MessagePlugin.success('编辑成功');
        remarkData.value = ''
        visible.value = false
        getRemarkList()
        setInfo(infoData.value)
      }).catch(err => {
        console.log(err, '啊啊打算的');

        MessagePlugin.error(err.response.data.message);
      })
    }
  }
  const toFromFlag = ref('pc_msg')
  const setInfo = (val) => {
    if (val.toFromFlag) {
      toFromFlag.value = val.toFromFlag
    }
    let openid = window.localStorage.getItem('openid')
    if (!openid || openid === 'undefined') {
      MessagePlugin.error('未获取到openid请重新登陆');
      return
    }
    vCardDetail(val.uuid, openid, toFromFlag.value).then(res => {
      const data = res.data.data;
      infoData.value = data;
      const setFlagToTrue = (arr: { flag: boolean }[]) => {
        arr.forEach(e => e.flag = true);
      };

      if (data.related_teams) {
        setFlagToTrue(data.related_teams);
      }
      if (data.related_associations) {
        setFlagToTrue(data.related_associations);
      }
      infoData.value.teamList = data.intact_teams
      infoData.value.associationList = data.intact_associations;
      console.log(infoData.value, 'infoData.valueinfoData.value');

      getRemarkList();
    });
    masData = val
    console.log(val, 'setInfosetInfosetInfo')
  }
  const telVisible = ref(false)
  const remarkListArr = ref([])
  const getRemarkList = () => {
    remarkList(infoData.value.id).then(res => {
      remarkListArr.value = res.data.data.list
    }).catch(err => {
      MessagePlugin.error(err.response.data.message);
    })
  }
  const subVcard = () => {
    folderCollect({
      card_id: infoData.value.id,
      from_type: 'client',
      from_openid: masData.openid ? masData.openid : window.localStorage.getItem('openid'),
    }).then(res => {
      MessagePlugin.success('保存成功!');
      setInfo(infoData.value)
    }).catch(err => {
      console.log(err, '阿萨达');

      MessagePlugin.error(err.response.data.message);
    })
  }
  const changeBtn = (val) => {
    if (val === '联系电话') {
      telVisible.value = true
    }
    if (val === '备注') {
      if (!infoData.value.collected) {
        const confirmDia = DialogPlugin.confirm({
          header: t('vcard.bzts'),
          body: t('vcard.bcmphcktjbz'),
          theme: 'info',
          confirmBtn: '保存',
          zIndex: 9999,
          top: '80px',
          attach: 'body',
          onConfirm: async () => {
            folderCollect({
              card_id: infoData.value.id,
              from_type: 'client',
              from_openid: masData.openid,
            }).then(res => {
              MessagePlugin.success('保存成功!');
              confirmDia.hide();
              // headerTitle.value = '备注'
              // dialogTitle.value = '备注'
              setInfo(infoData.value)
              visiblebz.value = true
              dialogTitle.value = '备注'
              addRemark()
              // getRemarkList()


            }).catch(err => {
              MessagePlugin.error(err.response.data.message);
            })
          },
          onClose: () => {
            confirmDia.hide();
          },
        });
      } else {
        visiblebz.value = true
        dialogTitle.value = '备注'
      }
    }
    console.log('setInfosetInfosetInfo')
  }

  const addRemark = () => {
    visible.value = true;
    dialogTitle.value = '新增备注'
  }
  const onClose = () => {
    console.log(headerTitle.value, 'setInfosetInfosetInfo')
    if (headerTitle.value === '名片详情') {
      emits('close-drawer')
    } else {
      headerTitle.value = '名片详情'
    }
  }

  defineExpose({
    setInfo,
  });

</script>
<style>
  .identity-info-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
  }

  .identity-item {
    width: 360px;
    height: 62px;
    display: flex;
    padding: 8px 8px 8px 12px;
    align-items: center;
    cursor: pointer;
    gap: 4px;
    border-radius: 8px;
    background: #FFF;
  }

  ::-webkit-scrollbar {
    width: 6px;
    background-color: transparent;
  }

  /*定义滚动条轨道 内阴影+圆角*/
  ::-webkit-scrollbar-track {
    background-color: transparent;
  }

  /*定义滑块 内阴影+圆角*/
  ::-webkit-scrollbar-thumb {
    border-radius: 4px;
    background: var(--icon-kyy_color_icon_transparent, rgba(26, 33, 57, 0.36));
  }

  .tool-vcard-drawer {

    .in-im-vcard-addbz {
      width: 376px;
      left: auto;
      right: 0;

      .t-drawer__content-wrapper--bottom {

        :deep(.t-drawer--bottom) {
          left: inherit !important;
          right: 0 !important;
        }

        .t-drawer--bottom {
          left: inherit !important;
          right: 0 !important;
        }
      }
    }
  }

  .vcard-drawer-in-im {
    .t-drawer__content-wrapper {
      overflow: hidden !important;
    }
  }

  .aaaaaaaaaaaaaaaaa {
    /* width: 376px;
    margin-top: 48px; */

    .footer-box {
      bottom: 48px;
    }

    .t-drawer__body {
      padding: 0 !important;

    }
  }

  .in-im-vcard-drawer-right-bz {
    height: calc(100vh - 48px);
    margin-top: 48px;
    width: 376px;

    .t-drawer__content-wrapper {
      overflow: hidden !important;
    }

    .t-drawer__content-wrapper--right {
      border-radius: 0 !important;

      .t-drawer__body {
        padding: 0 !important;
        margin-top: 0 !important;
        background: #fff;
      }
    }

    .t-drawer__content-wrapper {
      margin-top: 0 !important;
    }

    .t-drawer__content-wrapper--right,
    .t-drawer__content-wrapper {
      border-radius: 0 !important;

    }

    .t-drawer__content-wrapper--right.t-drawer__content-wrapper {
      border-radius: 0 !important;

    }

    .t-drawer__content-wrapper--right .t-drawer__content-wrapper {
      border-radius: 0 !important;

    }

    .t-drawer__header {
      background: #fff;
    }
  }

  .in-im-vcard-drawer {

    .t-icon-close {
      color: #516082 !important;
    }

    .company-info-item {
      border-radius: 8px;
      background: var(--bg-kyy_color_bg_card, rgba(255, 255, 255, 0.70));
    }
  }

  .in-win-vcard-drawer {
    overflow-x: hidden;

    .in-im-vcard-drawer {
      .t-drawer__content-wrapper {
        border-radius: 16px 16px 0 0;
      }

      .t-drawer__body {
        margin-top: 0 !important;
        padding: 0;
      }

      .t-drawer__header {
        background: #fff;
        border-radius: 16px 16px 0 0;
      }

      .changBtnDialogbody {
        height: 100% !important;
        background: linear-gradient(158deg, #FCF3FE 0%, #DEEBFD 100%) !important;

        .subbody {
          background-image: url('https://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/bg_img_vcard.png') !important;
          background-repeat: no-repeat !important;
          background-size: 100% auto !important;
        }
      }
    }

    .header-wrap {
      display: flex;
      padding: 16px;
      justify-content: space-between;

      .header-title {
        font-size: 16px;
        color: #1A2139;
        font-weight: 600;
      }

      .btn-close {
        width: 24px;
        height: 24px;
        cursor: pointer;
        color: #1A2139;
        font-size: 24px;
        -webkit-app-region: no-drag;
      }
    }
  }

  /* .in-win-vcard-drawer {
    height: calc(100vh - 130px) !important;
  } */

  /* .changBtnDialogbody::-webkit-scrollbar {
    width: 3px;
    height: 8px;
  }

  .changBtnDialogbody::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background: rgba(0, 0, 0, 0.1);
    -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.5);
  }

  .content-data::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background: rgba(0, 0, 0, 0.1);
    -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.5);
  }

  .content-data::-webkit-scrollbar {
    width: 3px;
    height: 8px;
  }

  .changBtnDialog::-webkit-scrollbar {
    width: 3px;
    height: 8px;
  }

  .content-data::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background: rgba(0, 0, 0, 0.1);
    -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.5);
  }

  .changBtnDialog::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background: rgba(0, 0, 0, 0.1);
    -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.5);
  } */

  .t-dialog.changBtnDialog {
    height: 620px;
    overflow: hidden;
    padding: 0 !important;
    margin-top: 50px;
    border-radius: 16px 16px 0 0 !important;
    position: absolute;
    bottom: 0;
    width: 376px;

    .t-dialog__header {
      padding: 16px 16px 0;
    }

    .t-dialog__body {
      padding-bottom: 0 !important;
      margin-bottom: 0 !important;
    }
  }

  .vcard-popconfirm-popup {
    .t-popup__content {
      padding: 0;
      border-radius: 8px;
    }

    .t-button {
      min-width: 52px;
      font-weight: 400;
    }

    .t-button__text {
      font-weight: 400 !important;

    }

    .t-button--theme-default {
      margin-right: 8px !important;
    }
  }

  .vcard-popup {
    .t-popup__content {
      padding: 4px;
      border-radius: 8px;
      max-height: 160px;
      overflow-y: auto;
    }

    .sj-icon {
      width: 232px;
      display: flex;
      align-items: center;
      overflow: hidden;
      color: var(--text-kyy_color_text_1, #1A2139);
      text-overflow: ellipsis;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      justify-content: space-between;
      padding: 5px 4px;
      cursor: pointer;
    }

    .sj-span {
      width: 135px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      word-break: break-all;

    }

    .address-icon {
      width: 360px;
      display: flex;
      align-items: center;
      overflow: hidden;
      color: var(--text-kyy_color_text_1, #1A2139);
      text-overflow: ellipsis;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      justify-content: space-between;
      padding: 5px 4px;
      cursor: pointer;
    }

    .address-span {
      width: 283px;
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      color: var(--text-kyy_color_text_1, #1A2139);
      text-overflow: ellipsis;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 157.143% */
    }

    .tel-icon {
      width: 192px;
      display: flex;
      align-items: center;
      overflow: hidden;
      color: var(--text-kyy_color_text_1, #1A2139);
      text-overflow: ellipsis;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      justify-content: space-between;
      padding: 5px 4px;
      cursor: pointer;
    }

    .tel-iconpark {
      font-size: 20px;
      color: #828DA5;
    }
  }
</style>
<style lang="less" scoped>
  .identity-item-name {
    color: #1A2139;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px;
  }

  .identity-item-desc {
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    width: 260px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    word-break: break-all;
  }

  .identity-item-right {
    font-size: 20px;
    color: #516082;
  }

  .remark-body {
    height: 619px !important;
    background: linear-gradient(158deg, #FCF3FE 0%, #DEEBFD 100%) !important;

    .subbody {
      height: 100%;
      background-image: url('https://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/bg_img_vcard.png') !important;
      background-repeat: no-repeat !important;
      background-size: 100% auto !important;
    }
  }

  .footer-box-add {
    background: var(--bg-kyy_color_bg_light, #FFF);
    display: flex;
    width: 100%;
    height: 57px;
    justify-content: center;
    align-items: center;
    position: absolute;
    bottom: 0;
    left: 0;
  }

  .in-win {
    max-height: 620px !important;
  }

  .play-icon {
    font-size: 36px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    cursor: pointer;
  }

  .footer-box {
    border-top: 1px solid #ECEFF5;
    display: flex;
    width: 100%;
    height: 57px;
    justify-content: center;
    align-items: center;
    position: absolute;
    bottom: 0;
    left: 0;
  }

  .bz {
    color: #828DA5;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    margin-top: 4px;
    /* 157.143% */
  }

  .tag-not-rz {
    display: inline-block;
    width: 44px;
    height: 20px;
    background: #ECEFF5;
    color: #516082;
    text-align: center;
    line-height: 20px;
    border-radius: 4px;
    font-size: 12px;
    margin-left: 4px;
  }

  .tag-rz {
    display: inline-block;
    width: 44px;
    height: 20px;
    background: #E0F2E5;
    border-radius: 4px;
    color: #499D60;
    text-align: center;
    line-height: 20px;
    font-size: 12px;
    margin-left: 4px;
  }

  .flexbox-a {
    display: flex;
    align-items: center;
    width: fit-content;
    height: 24px;
    cursor: pointer;
    border-radius: 99px;
    background: #EAECFF;
    color: #4D5EFF;
    text-align: center;
    font-size: 14px;
    font-weight: 400;
    line-height: 24px;
    justify-content: center;
    padding-left: 8px;
  }

  .flex-a {
    display: flex;
    align-items: center;
  }

  .haveid {
    background: #ECEFF5;
    color: #828DA5;
    padding-right: 8px;

  }

  .icon-box {
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .icon-box:hover {
    border-radius: 4px;
    background: var(--bg-kyy_color_bgBrand_hover, #EAECFF);
  }

  .remark-icon {
    font-size: 20px;
    color: #828DA5;
    text-align: center;
    line-height: 28px;
  }

  .content-item-content-remark {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0 16px;
    border-bottom: 1px solid #ECEFF5;
    padding-bottom: 12px;
    color: #828DA5;
  }

  .content-data {
    height: calc(100% - 57px);
    overflow-y: auto;
    padding: 8px 8px 0;
  }

  .remark-body {
    .content-data {
      padding: 0;
      width: 360px;
    }
  }

  .flie-item {
    display: flex;
    flex-wrap: wrap;
    width: 78px;
    height: 78px;
  }

  .file-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    padding: 0 12px;
  }

  .file-item-img {
    border-radius: 8px;
    border: 1px solid var(--kyy_color_upload_border_default, #D5DBE4);
    width: 100%;
    height: 198px;
    cursor: pointer;
    object-fit: cover;
  }

  .file-item-video {
    object-fit: cover;
    border-radius: 8px;
    border: 1px solid var(--kyy_color_upload_border_default, #D5DBE4);
    cursor: pointer;
    width: 100%;
    // height: 198px;
  }

  .specialVideo {
    height: 198px;
  }

  .vcard-list {
    flex: 1;
    overflow: auto;
    padding-left: 8px;
    padding-bottom: 56px;

    &.in-win {
      overflow-y: auto;
    }
  }

  .tag-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    padding: 0 12px 12px;
  }

  .del-vcard {
    position: absolute;
    bottom: 0;
    background-color: #fff;
    display: flex;
    height: 56px;
    padding: 12px;
    justify-content: flex-end;
    align-items: center;
    width: 100%;
    gap: 8px;
    border-top: 1px solid #ECEFF5;

    .t-button {
      border: 1px solid var(--color-button_secondaryBrand-kyy_color_button_secondaryBrand_border_dedault, #4D5EFF);
      background: var(--color-button_secondaryBrand-kyy_color_button_secondrayBorder_bg_default, #EAECFF);
    }
  }

  .tag-item {
    border-radius: var(--kyy_radius_tag_s, 4px);
    background: var(--kyy_color_tag_bg_gray, #ECEFF5);
    width: fit-content;
    color: var(--kyy_color_tag_text_gray, #516082);
    text-align: center;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    padding: 0 4px;
  }

  .content-item-remark {
    overflow-wrap: anywhere;
    padding-top: 12px;
    border-radius: 8px;
    background: var(--bg-kyy_color_bg_card, rgba(255, 255, 255, 0.70));
    margin-bottom: 8px;
  }

  .address-span-title {
    width: 283px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    word-break: break-all;
    overflow: hidden;
    color: var(--text-kyy_color_text_1, #1A2139);
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px;
    /* 157.143% */
  }

  .address-span-desc {
    width: 283px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    word-break: break-all;
    overflow: hidden;
    color: var(--text-kyy_color_text_2, #516082);
    text-overflow: ellipsis;

    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
  }

  .address-span-number {
    width: 283px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    word-break: break-all;
    overflow: hidden;
    color: var(--text-kyy_color_text_2, #516082);
    text-overflow: ellipsis;

    /* kyy_fontSize_2/regular */
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
  }


  .vcard-drawer {
    height: 100%;
    flex-direction: column;
    display: flex;
    flex-wrap: wrap;
    background: linear-gradient(158deg, #FCF3FE 0%, #DEEBFD 100%) !important;

    .subbody {
      height: 100%;
      background-image: url('https://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/bg_img_vcard.png') !important;
      background-repeat: no-repeat !important;
      background-size: 100% auto !important;

      display: flex;
    flex-direction: column;
    }

    .tag-list-box {
      margin: 0 12px 12px;

    }

    .content-item-title-remark {
      padding: 0 16px;
      min-height: 44px;
      color: #1A2139;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 157.143% */
    }

    .company-info-item:last-child {
      border-bottom: none;

    }

    .company-info {
      width: 360px;
      border-radius: 8px;
      margin: 12px 0;
      background: var(--bg-kyy_color_bg_card, rgba(255, 255, 255, 0.70));
    }

    .file-info-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      padding: 0 4px 8px;
    }

    .file-info-list-item {
      width: 100%;
      // height: 198px;
      border-radius: 8px;
      display: inline-flex;

      .file-img {
        width: 100%;
        height: 100%;
      }
    }

    .company-info-item {
      display: flex;
      align-items: center;
      margin: 0 8px 8px;
      border-bottom: 1px solid #ECEFF5;
      padding: 12px 8px;
    }

    .company-info-list {
      .company-info-item-img {
        width: 44px;
        height: 44px;
        font-size: 44px;
        border-radius: 50%;
        margin-right: 12px;
      }

      .company-info-item-content {
        // width: 231px;
        width: 100%;
        flex: 1;
        display: flex;
        flex-direction: column;
      }

      .company-info-item-name {
        color: var(--text-kyy_color_text_1, #1A2139);
        margin-bottom: 4px;
        display: inline-block;
        /* kyy_fontSize_2/regular */
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        /* 157.143% */
        width: 100%;
      }

      .company-info-item-tag {
        color: var(--text-kyy_color_text_2, #516082);
        width: 100%;
        flex: 1;
        /* kyy_fontSize_1/regular */
        font-family: "PingFang SC";
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
        /* 166.667% */
      }

      .company-info-item-img-lk {
        width: 44px;
        height: 50px;
        margin-left: 12px;
      }
    }

    .company-info-header-box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      padding: 8px 12px 0;
      margin-bottom: 8px;
    }

    .company-info-header-title {
      color: #1A2139;
      text-align: center;
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px;
      /* 157.143% */
    }

    .view-all-box {
      display: flex;
      cursor: pointer;
      color: #4D5EFF;
      font-size: 14px;
    }

    .block-lin {
      background: var(--divider-kyy_color_divider_light, #ECEFF5);
      width: 352px;
      height: 8px;
      margin: 0 auto;
    }

    .my-card-btns {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 360px;
      padding: 8px;
      border-radius: 8px;
      background: var(--bg-kyy_color_bg_card, rgba(255, 255, 255, 0.70));
    }

    .icon32 {
      font-size: 32px;
    }

    .lin {
      width: 1px;
      height: 16px;
      background: #ECEFF5;
      margin: 0 4px;
    }

    .btn-box {
      width: 60px;
      text-align: center;
      cursor: pointer;
    }

    .btn-text {
      color: var(--text-kyy_color_text_1, #1A2139);
      text-align: center;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 157.143% */
    }

    .header {
      display: flex;
      height: 56px;
      padding: 16px;
      gap: 12px;
      width: 100%;
      align-items: center;
      justify-content: space-between;
      -webkit-app-region: drag;
    }

    .content_text {
      color: #1A2139;
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
    }

    .btn-close {
      width: 24px;
      height: 24px;
      cursor: pointer;
      color: #1A2139;
      font-size: 24px;
      -webkit-app-region: no-drag;
    }
  }
</style>