import { createMerchant, reSeparateAccounts, separateAccounts, getMccList, getMerchantBusinessContent, getBankCodeList } from '@renderer/api/workBench/merchant';
import { ApplyBindImg, BankCodeItem, MerchantFormData, StatusType } from '@renderer/api/workBench/merchant.model';
import to from 'await-to-js';
import { ref, Ref, unref } from 'vue';
import { getTeams } from '@renderer/api/contacts/api/organize';
import { useMerchantStore } from '@renderer/store/modules/merchant';
import { orgTypeMap } from '@renderer/components/orgAuth/utils';
import debounce from 'lodash/debounce';

/**
 * 商户入网相关接口请求 hooks
 */
export const useMerchantRequest = () => {
  // 加载状态
  const createMerchantLoading = ref(false);
  const splitAccountLoading = ref(false);

  /**
   * 创建商户
   */
  const createMerchantInfo = async (data: MerchantFormData) => {
    createMerchantLoading.value = true;
    const [err, res] = await to(createMerchant(data));
    createMerchantLoading.value = false;

    if (err) {
      console.error('创建商户失败:', err);
      throw err;
    }

    return res.data.data;
  };

  /**
   * 通用分账申请处理
   */
  const handleSplitAccount = async (
    apiFn: (...args: any[]) => Promise<any>,
    apiArgs: any[],
  ) => {
    splitAccountLoading.value = true;
    const [err, res] = await to(apiFn(...apiArgs));
    splitAccountLoading.value = false;

    if (err) return { status: 'splitFailed' as StatusType };

    const data = res.data.data;
    const status = data.is_separate_accounts;
    return {
      status: { 1: 'splitPending', 2: 'splitSuccess', 3: 'splitFailed' }[status] as StatusType,
      data,
    };
  };

  /**
   * 提交分账申请
   */
  const submitSplitAccountApply = (applyBindImg: ApplyBindImg, teamId?: string) => handleSplitAccount(separateAccounts, [{ apply_bind_img: [applyBindImg] }, teamId]);

  /**
   * 重新提交分账申请
   */
  const resubmitSeparateAccounts = (merchant_id: number, teamId?: string) => handleSplitAccount(reSeparateAccounts, [{ merchant_id }, teamId]);

  return {
    createMerchantInfo,
    submitSplitAccountApply,
    resubmitSeparateAccounts,
    createMerchantLoading,
    splitAccountLoading,
  };
};

// 获取商户分类
export const useMerchantCategory = () => {
  const categoryOptions = ref([]);
  const loading = ref(false);

  const fetchMerchantCategory = async () => {
    loading.value = true;
    const [err, res] = await to(getMccList());
    loading.value = false;

    if (err) {
      console.error('获取商户分类失败:', err);
      return;
    }

    categoryOptions.value = res.data.data.list.map((item) => ({
      label: item.merchant_category_name,
      value: item.mcc_code,
    }));
  };

  return {
    categoryOptions,
    loading,
    fetchMerchantCategory,
  };
};

// 获取经营类目
export const useBusinessContent = () => {
  const contentOptions = ref([]);
  const loading = ref(false);

  const fetchBusinessContent = async () => {
    loading.value = true;
    const [err, res] = await to(getMerchantBusinessContent());
    loading.value = false;

    if (err) {
      console.error('获取经营类目失败:', err);
      return;
    }

    contentOptions.value = res.data.data.list.map((item) => ({
      label: item.code_name,
      value: item.code,
    }));
  };

  return {
    contentOptions,
    loading,
    fetchBusinessContent,
  };
};

/**
 * 组织认证状态
 * @param teamId 组织ID
 * @param loadingRef 可选，外部 loading ref
 * @param orgAuthDetailRef 可选，外部 orgAuthDetail ref
 * @param orgAuthVisibleRef 可选，外部 orgAuthVisible ref
 * @param orgTypeMap 组织类型映射（如 teamType2SquareType）
 */
export function useTeamAuthStatus(
  teamId?: Ref<string> | string,
  loadingRef?: Ref<boolean>,
  orgAuthDetailRef?: Ref<any>,
  orgAuthVisibleRef?: Ref<boolean>,
) {
  const merchantStore = useMerchantStore();
  const loading = loadingRef || ref(false);
  const orgAuthDetail = orgAuthDetailRef || ref<any>(null);
  const orgAuthVisible = orgAuthVisibleRef || ref(false);

  /**
   * 检查组织认证状态
   * @returns Promise<boolean> true: 需弹窗认证，false: 已认证
   */
  const fetchTeamAuthStatus = async (): Promise<boolean> => {
    loading.value = true;
    const id = unref(teamId) || merchantStore.teamId;
    const [err, res] = await to(getTeams({ teamId: id }));
    const data = res?.data?.data;

    if (err || res?.data?.code !== 0 || !data) {
      loading.value = false;
      return;
    }

    orgAuthDetail.value = { ...data, orgType: orgTypeMap.get(data.type)?.orgType };
    loading.value = false;

    // 0：未认证，1：已认证，2：认证审核中，3：认证已过期，4：已驳回
    return data.auth !== 1;
  };

  return {
    loading,
    orgAuthDetail,
    orgAuthVisible,
    fetchTeamAuthStatus,
  };
}

/**
 * 银行列表
 */
export const useBankList = () => {
  const bankList = ref<{ label: string; value: string; item: BankCodeItem }[]>([]);
  const loading = ref(false);

  const fetchBankList = debounce(async (keyword = '') => {
    loading.value = true;
    const [err, res] = await to(getBankCodeList(keyword));
    loading.value = false;

    if (err || !res?.data?.data) {
      console.error('获取银行列表失败:', err);
      return;
    }

    bankList.value = res.data.data.list.map((item) => ({
      label: item.bank_name,
      value: item.bank_name,
      item,
    }));
  }, 500);

  return {
    bankList,
    loading,
    fetchBankList,
  };
};
