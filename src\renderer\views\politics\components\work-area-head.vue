<template>
  <div v-lkloading="{ show: loading, height: false, opacity: true }" class="head-box">
    <iconpark-icon name="iconSpecial-graphics" class="icon-specials" />

    <div v-show="isShowTabsScrollIcon" class="tabs-arrow" @click="onScrollTabs(1)">
      <iconpark-icon name="iconarrowlift" style="font-size: 20px"></iconpark-icon>
    </div>
    <div ref="tabsListRef" class="tabs-list">
      <div class="tabs-list--box" :style="{ transform: `translate3d(${tabsScrollLeft}px, 0, 0)` }">
        <div
          v-for="(item, index) in store.tabs"
          :key="index"
          :class="['tabs-item', { 'bgc-fff': store.activeIndex === index }]"
          :title="item.title"
          @click.stop="switchTab(item, index)"
        >
          <div class="topTab">
            <!-- {{ item.parentPath }}
            {{ route.path }} -->
            <!-- {{ item.fullPath }} -->
            <!-- <div v-if="route.path === item.fullPath" class="left-head-color" /> -->
            <span class="tabIcon">
              <!-- <img class="tabImg" :src="TAB_ICON" alt="" /> -->
              <!-- <iconpark-icon
                  name="servicedefault"
                  class="servicedefault"
                ></iconpark-icon> -->

              <img v-if="item.icon === 'rich'" :src="business" class="tabImg" />
              <img v-else-if="item.icon === 'name'" :src="directory" class="tabImg" />
              <img v-else-if="item.icon === 'active'" :src="activity" class="tabImg" />
              <img v-else-if="item.icon === 'notice'" :src="notice" class="tabImg" />
              <img v-else-if="item.icon === 'document'" :src="document" class="tabImg" />
              <img v-else src="@renderer/assets/member/svg/member.svg" class="tabImg" />
            </span>

            <!-- <iconpark-icon class="iconTo" name="iconthumbnail" /> -->

            <span class="tab-title line-1">{{ item.title }}</span>
          </div>
          <!-- <img

            style="width: 12px; height: 12px; margin-left: 8px"
            src="@renderer/assets/img/<EMAIL>"
            @click.stop="removeTab(item)"
          /> -->
          <iconpark-icon
            v-if="route.path !== item.path && !item.affix"
            name="iconerror"
            style="font-size: 20px; margin-left: 8px"
            class="iconClose"
            @click.stop="removeTab(item)"
          ></iconpark-icon>
        </div>
      </div>
    </div>
    <div v-show="isShowTabsScrollIcon" class="tabs-arrow" @click="onScrollTabs(-1)">
      <iconpark-icon name="iconarrowright" style="font-size: 20px"></iconpark-icon>
    </div>
    <span>
      <t-popup placement="bottom-right" overlay-inner-class-name="square-account-list-popup">
        <div v-if="store.activeAccount" class="account">
          <!-- <img
              v-show="store.activeAccount.teamLogo"
              class="org-img"
              :src="store.activeAccount.teamLogo"
              alt=""
            />
            <img
              v-show="!store.activeAccount.teamLogo"
              class="org-img"
              src="@/assets/building-fill.png"
              alt=""
            /> -->
          <t-badge dot :count="0">
            <kyy-avatar
              class="rd-10"
              :avatar-size="'24px'"
              :image-url="store.activeAccount.teamLogo || ORG_DEFAULT_AVATAR"
              :user-name="store.activeAccount?.teamFullName"
              :shape="'circle'"
            />
          </t-badge>

          <div class="text">{{ store.activeAccount?.teamFullName }}</div>
          <!-- <img
              class="arrow"
              src="@renderer/assets/img/icon_arrow_down.svg"
              alt=""
            /> -->
          <iconpark-icon v-if="accountLists && accountLists.length > 1" name="iconarrowdown" class="arrow" />
        </div>
        <template #content>
          <div v-if="accountLists && accountLists.length > 0" class="account-wrap">
            <div
              v-for="item in accountLists"
              :key="item.idTeam"
              :class="['account-item', { active: item.teamId === store.activeAccount?.teamId }]"
              @click="accountClick(item)"
            >
              <!-- {{ item.teamId === store.activeAccount?.teamId }} -->
              <!-- <img v-if="item?.teamLogo" :src="item.teamLogo" />
                <img
                  v-else
                  class="org-img"
                  src="@/assets/building-fill.png"
                  alt=""
                /> -->
              <!-- dot -->

              <span class="left">
                <iconpark-icon
                  name="iconother"
                  :class="{
                    icon: true,
                    'op-0': item.teamId !== store.activeAccount?.teamId,
                  }"
                />
                <!--
                <t-badge :count="1" dot size="small" class="mr-5">

                </t-badge> -->
                <kyy-avatar
                  class="rd-10"
                  :avatar-size="'24px'"
                  :image-url="item?.teamLogo || ORG_DEFAULT_AVATAR"
                  :user-name="item?.teamFullName"
                  :shape="'circle'"
                />
                <div class="name line-1">{{ item.teamFullName }}</div>
              </span>
              <!-- 商协会1.2暂时去掉 -->
              <!-- <span class="right">
                <t-badge
                  :count="store.getCountByTeam(item.teamId)"
                  size="small"
                  class="mr-5"
                />
              </span> -->
            </div>
          </div>
        </template>
      </t-popup>
    </span>

    <span class="ml-10">
      <iconpark-icon name="iconrefresh" class="btn-op" @click="refresh"></iconpark-icon>
      <iconpark-icon v-if="newWinFlag" name="iconwindow" class="btn-op" @click="openStandAloneWin"></iconpark-icon>
    </span>
  </div>
</template>

<script setup lang="ts">
import business from "@renderer/assets/member/svg/business.svg";
import activity from "@renderer/assets/member/svg/activity.svg";
import directory from "@renderer/assets/member/svg/directory.svg";
import square from "@renderer/assets/member/svg/square.svg";
// import business from "@renderer/assets/member/svg/business.svg";
import document from "@renderer/assets/member/svg/document.svg";
import notice from "@/assets/niche/ggao2.svg";

import CountBadgeComp from "@renderer/components/member/components/CountBadgeComp.vue";

import TAB_ICON from "@renderer/assets/member/icon_apply_notes.svg";
import { useRoute, useRouter } from "vue-router";
import { ref, reactive, onMounted, toRaw, watch, nextTick } from "vue";
import to from "await-to-js";
import { usePoliticsStore } from "@renderer/views/politics/store/politics";
// import { GetCommonOrganizesAxios } from "@renderer/api/engineer";
import { getResponseResult } from "@renderer/utils/myUtils";
// import { individualInfo } from "@/api/square/home";
// import { MemberTeamsReqAxios } from "@renderer/api/engineer";
import { getCommonTeamsAxios } from "@renderer/api/politics/api/businessApi";
import { ORG_DEFAULT_AVATAR } from "@/views/square/constant";
import KyyAvatar from "@/components/kyy-avatar/index.vue";
import { setPoliticsTeamID } from "@renderer/views/politics/utils/auth";
import { DialogPlugin } from "tdesign-vue-next";
import { useI18n } from "vue-i18n";
import LynkerSDK from '@renderer/_jssdk';

const { ipcRenderer } = LynkerSDK;
const { t } = useI18n();

// 状态管理：控制移动tabs的偏移距离
const tabsScrollLeft = ref(0);
// 状态管理：获取tabs-list的dom使用
const tabsListRef = ref(null);
// 状态管理：是否现实移动的图标
const isShowTabsScrollIcon = ref(false);
const emit = defineEmits(["refresh", "loaded"]);

const route = useRoute();
const router = useRouter();
const store = usePoliticsStore();
// const tabList = store.tabs;
const loading = ref(false);
const newWinFlag = ref(true);

// const accountList = reactive([
//   {
//     squareId: "1",
//     avatar: "http://dummyimage.com/100x100",
//     name: "hgj_个人广场号",
//     count: 0,
//     type: 1,
//   },
//   {
//     squareId: "2",
//     avatar: "https://fakeimg.pl/200x200/",
//     name: "深圳速米科技有限公司",
//     count: 6,
//     type: 0,
//   },
// ]);
const accountLists = ref([]);

/**
 * 点击左右箭头图标移动tabs拦
 * @param normal 移动的方向
 */
const onScrollTabs = async (normal: -1 | 1, index = 0) => {
  await nextTick();
  const tabItemWidth = 180;
  tabsScrollLeft.value += tabItemWidth * normal * (index + 1);
  const scrollWidth = tabsListRef.value.scrollWidth;
  const offsetWidth = tabsListRef.value.offsetWidth;
  if (tabsScrollLeft.value < -scrollWidth + offsetWidth) {
    tabsScrollLeft.value = -scrollWidth + offsetWidth;
  } else if (tabsScrollLeft.value > 0) {
    tabsScrollLeft.value = 0;
  }
};

watch(
  () => store.tabs,
  async () => {
    await nextTick();
    const scrollWidth = tabsListRef.value.scrollWidth;
    const offsetWidth = tabsListRef.value.offsetWidth;
    isShowTabsScrollIcon.value = scrollWidth > offsetWidth;
  },
  {
    immediate: true,
    deep: true,
  },
);
watch(
  () => store.activeIndex,
  async (newVal: number, oldVal: number) => {
    await nextTick();
    onScrollTabs(newVal - oldVal > 0 ? -1 : 1, newVal);
  },
  {
    immediate: true,
  },
);

onMounted(() => {
  // TODO 获取所有广场号列表
  // setTimeout(() => {
  //   store.activeAccount = toRaw(accountList)[0];
  // }, 1000);
});

const onInitData = async () => {
  // store.activeAccount = null;
  loading.value = true;
  let [err, res] = await to(getCommonTeamsAxios({}));
  loading.value = false;
  console.log(err, res);
  if (err) return;

  // let [err, res] = await to(MemberTeamsReqAxios());
  res = getResponseResult(res);
  const resList = res.data ? res.data.filter((v) => v.teamType === 4) : [];
  // for (const item of resList) {
  //   item.name = item.teamFullName;
  //   item.icon = item.staffAvatar;
  //   item.id = item.teamId;
  // }
  accountLists.value = resList;
  // console.log(res.data?.team);
  console.log(res);
  console.log(store.activeAccount);
  console.log(store, "内容store");
  console.log(accountLists.value);
  if (!err) {
    // store.activeAccount = res.data?.profile || {};

    // 加多一层，别的地方跳过来的
    console.log("flag:", route.query, accountLists.value);
    if (route.query && route.query.from === "message" && accountLists.value.length > 0) {
      const { teamId } = route.query;
      const teamObj = accountLists.value.find((v) => v.teamId === teamId);
      console.log("flag:", teamId, teamObj);
      store.setActiveAccount(teamObj || accountLists.value[0]);
    } else if (store.activeAccount && accountLists.value.length > 0) {
      if (store.goTeamId) {
        // 跳转到对应的新组织
        const tem = accountLists.value.find((v) => v.teamId === store.goTeamId);
        store.setActiveAccount(tem);
        store.setGoTeam("");
      } else if (accountLists.value.some((v) => v.teamId === store.activeAccount.teamId)) {
        console.log("已存在");
        setPoliticsTeamID(store.activeAccount.teamId);
      } else {
        store.setActiveAccount(accountLists.value.length > 0 ? accountLists.value[0] : null);
      }
    } else {
      store.setActiveAccount(accountLists.value.length > 0 ? accountLists.value[0] : null);
    }

    console.log(res);
  }

  const tab = store?.tabs?.find((v) => v.name === "politics_number");
  console.log(tab);
  if (tab) {
    tab.title = "数字城市";
  }
};
onInitData();
// store.isBrushTeam = true;
watch(
  () => store.isBrushTeam,
  (val) => {
    if (val) {
      onLoadingShow(4000);
      onInitData();
      store.isBrushTeam = false;
    }
  },
);

watch(
  () => store.isLoadingShow,
  (val) => {
    if (val) {
      onLoadingShow(500);
      // onInitData();
      store.setLoadingShow(false);
    }
  },
);

// watch(() => route.fullPath, async () => {
//   setTimeout(() => {
//     if (route.query && route.query.from === 'message') {
//       const { teamId, redirect } = route.query;
//       if (teamId) {

//       }
//     }
//   });

// });

const onLoadingShow = (timer?) => {
  loading.value = true;
  setTimeout(() => {
    loading.value = false;
  }, timer || 900);
};

const accountClick = (item) => {
  if (store.tabs.length > 1) {
    const confirmDia = DialogPlugin({
      header: "提示",
      theme: "info",
      body: "切换组织将会关闭已打开的标签页面，请确保已妥善保存。",
      closeBtn: null,
      confirmBtn: "确定",
      zIndex: 6000,
      className: "delmode",
      onConfirm: async () => {
        store.removeAllTab();
        store.switchTab(0);
        if (store.tabs.length > 0) {
          router.push(store.tabs[0].fullPath);
        }

        // 删除字段操作
        confirmDia.hide();
        accountClickAfter(item);
      },
      onClose: () => {
        confirmDia.hide();
      },
    });
  } else {
    store.switchTab(0);
    router.push(store.tabs[0].fullPath);
    accountClickAfter(item);
  }
};
const accountClickAfter = (item) => {
  onLoadingShow();
  // store.activeAccount = item;
  store.setActiveAccount(item);

  // const activeTab = store.getActiveTab();
  // if (activeTab && activeTab.fullPath === "/politicsIndex/politics_manage") {
  //   store.removeTab(activeTab.fullPath);
  //   router.replace(store.getActiveTab().fullPath);
  // }
  // if (activeTab && activeTab.fullPath === "/politicsIndex/politics_home") {
  //   store.removeTab(activeTab.fullPath);
  //   router.replace(store.getActiveTab().fullPath);
  // }

  const tab = store?.tabs?.find((v) => v.name === "politics_number");
  console.log(tab);
  console.log(store);
  console.log(store.activeAccount);
  console.log("accountClick");
  if (tab) {
    tab.title = "组织端";
  }
};

const switchTab = (item, index) => {
  console.log("switchTab");
  // onLoadingShow();

  // if (item.fullPath === "/politicsIndex/member_manage") return;
  // store.removeTab("/politicsIndex/member_manage"); // 移除依赖页面
  // store.switchTab(index);
  // router.replace(item.fullPath);

  // const tab = store?.tabs?.find((v) => v.name === 'politics_number');
  // console.log(tab, 'tab');
  // if (tab) {
  //   tab.title = tab.title? tab.title: '数字城市';
  // }

  store.switchTab(index);
  let query = "";
  if (item.query) {
    console.log("🚀 ~ switchTab ~ item.query:", item.query);
    query = `?${new URLSearchParams(item.query).toString()}`;
  }

  router.replace(`${item.path}${query}`);
};

const removeTab = (item) => {
  // console.log(item.fullPath);
  // console.log(store.getActiveTab().fullPath);
  // onLoadingShow();
  // if (store.getActiveTab().fullPath === item.fullPath) {
  //   console.log('A')
  //   store.removeTab(item.);
  // } else {
  //   console.log('B')

  //   store.removeTab(item.fullPath, true);
  // }
  // console.log("275", store.getActiveTab());
  // router.replace(store.getActiveTab().fullPath);

  if (item.path === "member_manage") {
    const confirmDia = DialogPlugin({
      header: t("approval.desgin.header"),
      body: t("approval.desgin.body"),
      confirmBtn: t("approval.desgin.confirmBtn"),
      className: "dialog-classp24",
      cancelBtn: t("approval.desgin.cancelBtn"),
      onConfirm: ({ e }) => {
        // emits("design-goback", true);
        confirmDia.hide();
        actionRemoveTab(item);
      },
      onClose: ({ e }) => {
        confirmDia.hide();
      },
    });
  } else {
    actionRemoveTab(item);
  }
};

const actionRemoveTab = (item) => {
  item && store.removeTab(item);
  const currentRoute = store.getActiveTab();
  console.log(currentRoute);
  if (!currentRoute) return;
  router.replace({ path: currentRoute.path, query: currentRoute.query });
};

const refresh = () => {
  onLoadingShow();
  onInitData();
  emit("refresh");

  // router.go(0);
};

const openStandAloneWin = () => {
  newWinFlag.value = false;
  // ipcRenderer.invoke('click-standalone-window', {
  //   url: 'squareLayout',
  //   flag: route.fullPath.split('/')[1],
  // });

  console.log(route, "routerouteroute");

  ipcRenderer.invoke("click-standalone-window", {
    url: "projectLayout",
    flag: route.fullPath.split("/")[1],
  });
};
</script>

<style lang="less" scoped>
@import "@renderer/views/engineer/less/common.less";

:deep(.t-image__wrapper) {
  z-index: 0 !important;
}

.backgrd {
  background: #272b4f;
}
.bgc-fff {
  background: #fff !important;
  .iconClose {
    color: #3e4cd1;
  }
  &::after {
    display: none;
  }
}

.topTab {
  display: flex;
  align-items: center;
  gap: 8px;
  .tabIcon {
    // margin-left: 7px !important;
    border-radius: 4px !important;
    width: 20px;
    height: 20px;
    // background: var(--cyan-kyy-color-cyan-default, #11bdb2) !important;
    background: #fd9d4f;
    display: flex;
    align-items: center;
    justify-content: center;
    .tabImg {
      width: 20px !important;
      height: 20px !important;
      margin-right: 0 !important;
    }
    .servicedefault {
      font-size: 14px;
      color: #fff;
    }
  }
  .tab-title {
    overflow: hidden;
    color: var(--kyy_color_tabbar_item_text, #1a2139);
    text-overflow: ellipsis;
    max-width: 90px;
    /* kyy_fontSize_2/regular */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
}

.btn-op {
  width: 20px;
  height: 20px;
  font-size: 20px;
  margin-right: 16px;
  cursor: pointer;
}
.org-img {
  width: 24px;
  height: 24px;
  border-radius: 5px;
  margin-left: 4px;
}
.account {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  max-width: 214px;
  .arrow {
    width: 16px;
    height: 16px;
    // margin-right: 16px;
  }
  .text {
    // max-width: 94px;
    font-size: 12px;
    font-weight: 400;
    margin: 0 4px;
    color: #13161b;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.head-box {
  height: 40px;
  display: flex;
  align-items: center;
  overflow: hidden;
  border-radius: 8px 0px 0px 0px;
  // background: var(--kyy_color_tabbar_bg, #ebf1fc);
  background-color: var(--kyy_color_tabbar_bg, #ebf1fc);
  .tabs-item {
  }

  .tabs-list {
    &:hover {
      //overflow-x: overlay !important;
    }
    display: flex;
    flex: 1;
    width: 0;
    overflow-x: hidden;
    background: var(--kyy_color_tabbar_bg, #ebf1fc);
    &--box {
      display: flex;
      transition: transform 0.5s;
    }
    .tabs-item {
      cursor: pointer;
      min-width: 140px;
      max-width: 168px;

      justify-content: space-between;
      height: 32px;
      margin: 4px;
      display: flex;
      align-items: center;
      // background: #f1f2f5;
      border-radius: 4px;
      // background: var(--kyy_color_tabbar_item_bg_active, #fff);
      background: var(--kyy_color_tabbar_bg, #ebf1fc);

      border-radius: 4px;
      padding: 4px 12px;
      position: relative;
      &::after {
        position: absolute;
        content: " ";
        height: 20px;
        min-width: 1px;
        max-width: 1px;
        background-color: #d5dbe4;
        right: 0;
      }

      &:hover {
        background: var(--kyy_color_tabbar_item_bg_hover, rgba(255, 255, 255, 0.5));
        .iconClose {
          color: #707eff;
        }
        &::after {
          display: none;
        }
      }

      // box-shadow: 0 1px 4px 0 rgba(19, 22, 27, 0.24);
      .left-head-color {
        width: 5px;
        border-radius: 4px 0 0 4px;
        height: 32px;
        background: #2069e3;
      }
      img {
        width: 15px;
        height: 15px;
        margin-right: 0 !important;
      }
      span {
        height: 20px;
        font-size: 12px;
        font-weight: 400;
        color: #13161b;
        line-height: 20px;
      }
    }
  }
}
</style>

<style lang="less" scoped>
.tabs-arrow {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 35px;
  border-radius: 5px 0 0 0;
  background: var(--kyy_color_tabbar_bg, #ebf1fc);
  &:hover {
    cursor: pointer;
    background: var(--kyy_color_tabbar_item_bg_hover, rgba(255, 255, 255, 0.5));
  }
}
.icon-specials {
  width: 8px;
  height: 8px;
  position: absolute;
  top: 0;
  left: 0;
  color: var(--bg-kyy-color-bg-software-foucs, #272b4f);
}

.square-account-list-popup {
  //width: 311px;
  .account-wrap {
    max-height: 276px;
    overflow-y: auto;
  }
  .account-item {
    display: flex;
    align-items: center;
    height: 36px;
    border-radius: 4px;
    margin-bottom: 4px;
    padding: 0 8px 0 12px;
    justify-content: space-between;

    .left {
      display: flex;
      align-items: center;
      gap: 12px;
      .icon {
        font-size: 20px;
      }
    }
    .right {
      display: flex;
      align-items: center;
      padding-right: 8px;
    }
    // gap: 12px;
    cursor: pointer;
    &.active,
    &:hover {
      background: var(--lingke-select, #e1eaff);
    }
    > .icon {
      margin-right: 8px;
      font-size: 20px;
    }
  }
  .avatar {
    width: 24px;
    height: 24px;
    border-radius: 5px;
    margin-right: 8px;
  }
  .name {
    max-width: 220px;
    margin-right: 20px;
    color: var(--lingke-black-90, #1a2139);

    /* kyy_fontSize_2/regular */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
  }
}

.account {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-left: 50px;
  .text {
    color: var(--text-kyy-color-text-2, #516082);

    /* kyy_fontSize_2/regular */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
  }
  .arrow {
    font-size: 24px;
  }
}
:deep(.t-badge--dot) {
  right: 3px;
  margin-top: 2px;
}
:deep(.t-badge--circle) {
  height: 20px;
  line-height: 20px;
  font-size: 12px;
  padding: 0 8px;
  // color: var(--kyy_color_tag_text_magenta, #ff4aa1);
  color: var(--kyy_color_tag_text_magenta, #ff4aa1);
  // background: var(--kyy_color_tag_bg_magenta, #ffe3f1);
  background: var(--kyy_color_tag_bg_magenta, #ffe3f1);

  font-family: PingFang SC;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
}
</style>
