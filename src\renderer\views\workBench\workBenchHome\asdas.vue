<template>
  <div class="work-bench-home-view about" :class="isPromotionalpage?'isblue':''">
    <div class="boxContent">
      <WorkHomeHeader v-if="!isPromotionalpage" :activationGroupItem="activationGroupItem"
        @parentSetWorkBenchTabItem="setWorkBenchTabItem" @updisPromotionalpage="updisPromotionalpage"></WorkHomeHeader>
      <Promotionalpage v-if="isPromotionalpage" @updisPromotionalpage="updisPromotionalpage" :isShowBtn="true">
      </Promotionalpage>
      <ModuleList v-if="!isPromotionalpage" :activationGroupItem="activationGroupItem"
        @setWorkBenchTabItem="setWorkBenchTabItem" :checkIsAdminData="checkIsAdminData"></ModuleList>
    </div>
    <Tricks :offset="{ x: '-32', y: '-40' }" uuid="数智工场-首页" />

  </div>
</template>

<script setup lang="ts" name="workBenchHome">
  import { getLang } from "@/utils/auth";

  import { useRoute, useRouter } from "vue-router";
  import { onMounted, onActivated, ref, watch, nextTick } from "vue";
  import { useI18n } from "vue-i18n";
  import { onMountedOrActivated } from "@/hooks/onMountedOrActivated";
  import Promotionalpage from "../components/Promotionalpage.vue";
  import WorkHomeHeader from "../components/WorkHomeHeader.vue";
  import ModuleList from "../components/ModuleList.vue";
  import { filterImg, openAppListFn } from "../utils.ts";
  import { checkIsAdmin } from "@renderer/api/workBench";
  import LynkerSDK from '@renderer/_jssdk';

  const { ipcRenderer, shell } = LynkerSDK;
  const checkIsAdminData = ref(null);
  const router = useRouter();
  const route = useRoute();
  const { t } = useI18n();
  const isPromotionalpage = ref(false);

  console.log(route, 'routeroute');

  const props = defineProps({
    activationGroupItem: {
      type: Object,
      default: () => { },
    },
    tabList: {
      type: Array,
      default: () => [],
    },
  });
  const emits = defineEmits(["setWorkBenchTabItem", "fistTab", "add"]);

  const setWorkBenchTabItem = (val) => {
    emits("setWorkBenchTabItem", val);
  };
  const add = () => {
    emits("add");
  };

  const getCheckAdmin = async () => {
    console.log("getCheckAdmin zhixing");
    console.log({ ...props.activationGroupItem.user_ids }, props.activationGroupItem.teamId);
    const res = await checkIsAdmin({ ...props.activationGroupItem.user_ids }, props.activationGroupItem.teamId);
    console.log(res?.data);
    checkIsAdminData.value = res?.data?.data;
    console.log(checkIsAdminData.value);
  };

  const updisPromotionalpage = (flag) => {
    if (flag) {
      window.localStorage.removeItem("isPromotionalpage");
      isPromotionalpage.value = true;
      return;
    }
    isPromotionalpage.value = false;
    window.localStorage.setItem("isPromotionalpage", true);
  };
  watch(
    () => props.activationGroupItem.teamId,
    (newValue) => {
      console.log(route.query, 'route.query我看看');

      if (!route.query?.istab && route.query?.toAdmin && !route.query?.platform) {
        pushRouter();
        getCheckAdmin();
      }
    },
    { deep: true });
  const pushRouter = () => {
    console.log(route.query, "route.queryroute.queryroute.query");

    if (route.query?.singleTab) {
      router.push({
        path: `/workBenchIndex/AdDetails`,
        query: {
          id: route.query.id.toString(),
          teamId: route.query.teamId || currentTeamId.value,
          flag: "edit",
        },
      });
      ipcRenderer.invoke("set-work-bench-tab-item", {
        path: `/workBenchIndex/AdDetails`,
        name: "bench_ad_details",
        path_uuid: route.query.uuid,
        title: t('ad.ggxq'),
        type: route.query.type - 0,
        netCover: true,
        addNew: true,
        query: {
          id: route.query.id.toString(),
          teamId: route.query.teamId || currentTeamId.value,
          flag: "edit",
        },
      });
      return
    }
    if (route.query.jumpPath && route.query.uuid && !props.activationGroupItem.noJump && !route.query?.singleTab) {
      let appName = filterImg({
        uuid: route.query.uuid,
        name: route.query.name,
      }).name;
      emits("fistTab");
      router.push(
        openAppListFn(
          {
            uuid: route.query.uuid,
            name: appName,
          },
          props.activationGroupItem,
          route.query,
        ),
      );
    }

  };
  onMountedOrActivated(() => {
    isPromotionalpage.value = JSON.parse(window.localStorage.getItem("isPromotionalpage")) ? false : true;
    if (props.activationGroupItem.teamId) {
      getCheckAdmin();
    }
    emits("setWorkBenchTabItem", {
      activeIcon: "workshop",
      icon: "workshop",
      path: "/workBenchIndex/workBenchHome",
      path_uuid: "workBench",
      title: t("banch.banch"),
      iswork: true,
      name: "workBenchHome",
      type: 17,
    });
  });
</script>

<style lang="less" scoped>
  .isblue {
    background: #0F1595 !important;
  }

  .about {
    width: 100%;
    height: 100%;
    background: url("@/assets/bench/workbenchbg.svg");
    display: flex;
    flex-direction: column;
    // justify-content: center;
    align-items: center;
    padding: 16px 0 0 0;

    .boxContent {
      width: 1184px;
      /* width: 100%; */
    }
  }

  .work-bench-home-view {
    background-color: #f5f8fe;
    display: flex;
    flex-direction: column;
  }

  .work-bench-home {
    background-repeat: no-repeat;
    background-size: 100%;
    position: absolute;
    top: 40px;
    left: 50%;
    width: 1216px;
    height: 636px;
    z-index: 999;
  }

  .work-bench-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 30%;
  }

  .lin-text {
    color: var(--text-kyy_color_text_2, #516082);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
    position: relative;
    padding-left: 8px;
  }

  .lin-text::after {
    content: "";
    width: 2px;
    height: 44px;
    border-radius: 1px;
    background: #79a8d8;
    margin-left: 8px;
    position: absolute;
    top: 0;
    left: -8px;
  }

  .work-bench-item {
    background-size: 100% 100%;
    background-repeat: no-repeat;
    display: flex;
    padding: 24px 20px;
    flex-direction: column;
    align-items: flex-start;
    position: relative;
    width: 279px;
    height: 148px;

    .work-bench-head-item {
      margin-bottom: 8px;
      display: flex;
      align-items: center;
    }

    .work-bench-head-item-text {
      /* kyy_fontSize_3/bold */
      font-family: "PingFang SC";
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: 24px;
      /* 150% */
      background: linear-gradient(180deg, #74aae4 0%, #2f76c2 100%);
      -webkit-background-clip: text;
      /*将设置的背景颜色限制在文字中*/
      -webkit-text-fill-color: transparent;
      /*给文字设置成透明*/
    }

    img {
      width: 42px;
      height: 48px;
      margin-right: 8px;
    }
  }

  .gobench {
    display: flex;
    width: 360px;
    padding: 12px 24px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    text-align: center;
    height: 50px;
    line-height: 50px;
    border-radius: 40px;
    background: linear-gradient(90deg, #4d5eff 15.8%, #7e56ff 100%);
    color: var(--text-kyy_color_text_white, #fff);
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    margin: 36px auto 0;
    cursor: pointer;
  }

  .item1 {
    background-image: url("@/assets/bench/item.svg");
  }

  .item2 {
    background-image: url("@/assets/bench/item1.svg");

    .work-bench-head-item-text {
      background: linear-gradient(180deg, #7ebed9 0%, #3088ad 100%);
      -webkit-background-clip: text;
      /*将设置的背景颜色限制在文字中*/
      -webkit-text-fill-color: transparent;
      /*给文字设置成透明*/
    }

    .lin-text::after {
      background: #76b7d4;
    }
  }

  .item3 {
    background-image: url("@/assets/bench/item2.svg");

    .work-bench-head-item-text {
      background: linear-gradient(180deg, #cba981 0%, #aa7435 100%);
      -webkit-background-clip: text;
      /*将设置的背景颜色限制在文字中*/
      -webkit-text-fill-color: transparent;
      /*给文字设置成透明*/
    }

    .lin-text::after {
      background: #cfaf8b;
    }
  }

  .item4 {
    background-image: url("@/assets/bench/item3.svg");

    .work-bench-head-item-text {
      background: linear-gradient(180deg, #b3beef 0%, #4254a3 100%);
      -webkit-background-clip: text;
      /*将设置的背景颜色限制在文字中*/
      -webkit-text-fill-color: transparent;
      /*给文字设置成透明*/
    }

    .lin-text::after {
      background: #a8b5ec;
    }
  }

  ::-webkit-scrollbar {
    width: 4px;
    height: 4px;
    background-color: #f5f5f5;
  }

  /*定义滚动条轨道 内阴影+圆角*/
  ::-webkit-scrollbar-track {
    background-color: #e3e6eb;
  }
</style>
