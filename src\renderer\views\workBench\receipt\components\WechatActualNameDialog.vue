<script setup lang='ts'>
import { getPlatformAgreement } from '@renderer/api/workBench/merchant';
import { onMountedOrActivated } from '@renderer/hooks/onMountedOrActivated';
import { useMerchantStore } from '@renderer/store/modules/merchant';
import { useVModel } from '@vueuse/core';
import to from 'await-to-js';
import { ref, withDefaults, defineEmits, computed } from 'vue';

interface Props {
  visible?: boolean;
  source?: 'settlement-join';
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
});

const emit = defineEmits(['update:visible', 'submit']);
const visible = useVModel(props, 'visible', emit);
const merchantStore = useMerchantStore();

const isSettlementJoin = computed(() => props.source === 'settlement-join');
const confirmBtnText = computed(() => (isSettlementJoin.value ? null : '已绑定'));
const cancelBtnText = computed(() => (isSettlementJoin.value ? '关闭' : '取消'));

const loading = ref(true);
const qrCodeUrl = ref('');

// 获取微信聚合支付实名认证二维码
const getAgreement = async () => {
  const [err, res] = await to(getPlatformAgreement(merchantStore.teamId));
  if (err) {
    loading.value = false;
    return;
  }

  qrCodeUrl.value = res.data.data.account_opening_intention_url;
  loading.value = false;
};

onMountedOrActivated(() => {
  getAgreement();
});

const handleCancel = () => {
  visible.value = false;
  emit('update:visible', false);
};
</script>

<template>
  <t-dialog
    v-model:visible="visible"
    header="微信聚合支付实名认证"
    width="462"
    attach="body"
    :confirm-btn="confirmBtnText"
    :cancel-btn="cancelBtnText"
    prevent-scroll-through
    class="wechat-actual-name-dialog"
    :close-btn="true"
    @confirm="emit('submit')"
    @cancel="handleCancel"
  >
    <template #closeBtn>
      <iconpark-icon name="iconerror" class="text-24 cursor-pointer" />
    </template>

    <div class="content">
      <t-alert theme="info" title="该二维码实名用于桌面端聚合支付时，可进行微信扫码支付" />

      <div v-loading="loading" class="qrcode-container">
        <img
          v-if="qrCodeUrl"
          :src="qrCodeUrl"
          class="qrcode"
        >
      </div>
    </div>
  </t-dialog>
</template>

<style lang='less'>
.wechat-actual-name-dialog {
  .t-dialog {
    padding: 0;
    background-image: url('@/assets/square/gradient-bg.png');
    background-size: cover;
    background-position: center;
    border: 1px solid var(--divider-kyy_color_divider_light, #eceff5);
    box-shadow: 0px 12px 24px 0px rgba(0, 0, 0, 0.12);
    display: flex;
    flex-direction: column;
  }

  .t-dialog__header {
    padding: 16px 16px 16px 24px;
  }

  .t-dialog__body {
    flex: 1;
    border-radius: 12px;
    padding: 0px 12px 0 12px;
    margin-bottom: 12px;
  }

  .t-dialog__footer {
    display: flex;
    padding: 24px;
    justify-content: flex-end;
    align-items: center;
    background: var(--bg-kyy_color_bg_light, #fff);
    border-bottom-left-radius: 16px;
    border-bottom-right-radius: 16px;
  }

  .content {
    min-height: 246px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 24px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    background: var(--bg-kyy_color_bg_light, #FFF);
    padding: 16px 12px 24px 12px;
    border-radius: 8px;
    background: var(--bg-kyy_color_bg_default, #FFF);
  }

  .t-alert {
    display: flex;
    padding: 8px 24px;
    justify-content: center;
    align-items: center;
    gap: 8px;
    border-radius: 8px;
    background: var(--kyy_color_alert_bg_bule, #EAECFF);

    .t-alert__icon {
      display: none;
    }

    .t-alert__content {
      margin-left: 0;
    }

    .t-alert__title {
      color: var(--text-kyy_color_text_1, #1A2139);
      font-size: 14px;
      font-weight: 400;
      line-height: 22px;
    }

    .t-alert__message {
      display: none;
    }
  }

  .qrcode-container {
    width: 144px;
    height: 144px;
    border: 1px solid #e7e7e7;
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    .qrcode {
      display: flex;
      width: 132px;
      height: 132px;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      border-radius: 8px;
    }
  }
}
</style>
