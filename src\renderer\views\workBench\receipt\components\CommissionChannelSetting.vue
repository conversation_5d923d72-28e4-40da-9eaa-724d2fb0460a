<script setup lang="ts">
import { getCommissionChannels } from '@renderer/api/workBench/commission';
import to from 'await-to-js';
import { DialogPlugin } from 'tdesign-vue-next';
import { computed, onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
import { useMerchantStore } from '@renderer/store/modules/merchant';
import LynkerSDK from '@renderer/_jssdk';
import { useWorkBenchNavigate } from '../hooks/useNavigate';
import CommissionRateSettingDialog from './CommissionRateSettingDialog.vue';
import { filterImg } from '../../utils';

const props = defineProps<{
  teamId?: string;
}>();

const route = useRoute();
const teamId = computed(() => props.teamId || route.query?.teamId as string);
const merchantStore = useMerchantStore();
const { toCommissionSharing } = useWorkBenchNavigate();

const columns = ref([
  { colKey: 'channel_name', cell: 'channel_name', title: '分佣渠道' },
  { colKey: 'commission_rate', title: 'title-slot-name', width: '35%', cell: (h, { row }) => `${row.commission_rate}%` },
  { colKey: 'action', cell: 'action', title: '操作', width: '27%' },
]);
const tableData = ref([]);
const loading = ref(true);

const getImgByUUID = (uuid: string) => filterImg({ uuid }).img;

const getInfo = async () => {
  loading.value = true;
  const [err, res] = await to(getCommissionChannels(teamId.value));
  loading.value = false;
  if (err) return;

  tableData.value = [res.data.data.detail].map((item) => ({
    ...item,
    iconImg: getImgByUUID(item.application_uuid),
  }));
};

onMounted(() => {
  getInfo();
});

// 检查商户入网状态
const checkMerchantStatus = () => {
  // 若商户分佣未成功（包括商户未入网、商户入网失败、商户入网成功但未分佣申请）
  const failed = !merchantStore.isExist || merchantStore.status.is_open_merchant === '3' || merchantStore.status?.is_receiver_no === '0';
  if (failed) {
    const confirmDia = DialogPlugin.confirm({
      header: '提示',
      body: '当前组织未成功入网，请入网成功后再设置分佣比例',
      confirmBtn: '入网',
      closeBtn: false,
      closeOnOverlayClick: false,
      theme: 'info',
      onConfirm: async () => {
        confirmDia.destroy();
        toCommissionSharing();
      },
      onCancel: async () => {
        confirmDia.destroy();
      },
    });
    return false;
  }
  return true;
};

// TODO 切换到数字平台，并选择当前组织
const handleOpen = () => {
  LynkerSDK.mainMenu.openDigitalPlatform({
    teamId: teamId.value,
    query: {
      teamId: teamId.value,
    },
  });
};

const commissionRateSettingRef = ref<InstanceType<typeof CommissionRateSettingDialog>>();

// 编辑分佣比例
const handleEdit = (row) => {
  if (!checkMerchantStatus()) return;

  commissionRateSettingRef.value.open({
    type: row.application_uuid,
    maxRate: row.max_commission_rate,
  });
};
</script>

<template>
  <div class="page-wrap">
    <div class="tips">
      <p>其他店铺的商品在自身数字平台上展示并交易成功后所分得得佣金比例设置。</p>
      <p>注意：</p>
      <p>1、其他店铺在自身数字平台上展示时，请双方线下签署好协议，防止后续纠纷<span class="color-[#D54941]">（渠道分佣关联时会下载协议签署）。</span></p>
      <p>2、设置分佣比例时，需要进行入网分佣，否则无法进行线上分账打款</p>
      <p>3、每次调整分佣比例时都会发送消息通知已经关联的组织</p>
    </div>

    <t-table
      :loading="loading"
      row-key="id"
      :data="tableData"
      :columns="columns"
    >
      <template #title-slot-name>
        <div class="flex items-center gap-4">
          分佣比例
          <t-tooltip
            placement="bottom"
            content="另可平台会控制数字平台分佣比例范围，防止数字平台因设置比例过高影响市场平衡"
            :show-arrow="false"
            :overlay-inner-style="{ maxWidth: '300px' }"
          >
            <t-icon name="help-circle" class="text-16 color-text-3 cursor-pointer" />
          </t-tooltip>
        </div>
      </template>
      <template #channel_name="{ row }">
        <div class="flex items-center gap-4">
          <span class="tag tag-blue">自</span>
          <div v-if="row.application_uuid" class="channel-name">
            <img :src="row.iconImg" class="w-16 h-16" alt="icon">
            {{ row.channel_name }}
          </div>
          <template v-else>
            <div class="no-operation">
              <iconpark-icon name="iconwarning" class="text-16" />
              未开启数字平台
            </div>
            <t-button
              theme="primary"
              variant="outline"
              size="small"
              @click="handleOpen"
            >
              去开启
            </t-button>
          </template>
        </div>
      </template>
      <template #action="{ row }">
        <t-button theme="primary" variant="text" @click="handleEdit(row)">
          编辑
        </t-button>
      </template>
    </t-table>

    <CommissionRateSettingDialog ref="commissionRateSettingRef" :team-id="teamId" @success="getInfo" />
  </div>
</template>

<style scoped lang="less">
.page-wrap {
  width: 100%;
  height: 100%;
}

.tips {
  margin-bottom: 16px;
  padding: 8px 16px;
  border-radius: 8px;
  background: var(--kyy_color_alert_bg_bule, #EAECFF);
  color: var(--kyy_color_alert_text, #1A2139);
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
}

.tag {
  display: inline-block;
  height: 20px;
  line-height: 20px;
  min-height: 20px;
  max-height: 20px;
  padding: 0px 4px;
  gap: 4px;
  flex-shrink: 0;
  border-radius: var(--kyy_radius_tag_s, 4px);

  &-blue {
    color: var(--kyy_color_tag_text_kyyBlue, #21ACFA);
    background: var(--kyy_color_tag_bg_kyyBlue, #E4F5FE);
  }
}

.channel-name {
  display: inline-flex;
  padding: 0px 4px;
  align-items: center;
  gap: 2px;
  border-radius: 4px;
  background: var(--bg-kyy_color_bg_deep, #F5F8FE);
  color: var(--brand-kyy_color_brand_default, #4D5EFF);
  font-size: 12px;
  font-weight: 400;
  line-height: 20px;
}

.no-operation {
  display: flex;
  height: 24px;
  padding: 2px 4px;
  align-items: center;
  gap: 4px;
  border-radius: 4px;
  background: var(--tagBG-kyy_color_tagBg_warning, #FFE5D1);
  color: var(--warning-kyy_color_warning_default, #FC7C14);
  font-size: 12px;
  font-weight: 400;
  line-height: 20px;
}

:deep(.t-button--variant-text.t-button--theme-primary) {
  padding: 4px !important;
  height: 30px !important;
  color: var(--kyy_color_switch_brand_default, #4D5EFF) !important;
  &:hover {
    border-radius: 4px;
    background-color: var(--bg-kyy_color_bg_list_foucs, #E1EAFF) !important;
  }
}
</style>
