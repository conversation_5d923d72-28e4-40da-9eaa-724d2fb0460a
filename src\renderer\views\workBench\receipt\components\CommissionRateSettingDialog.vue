<script setup lang="ts">
import { setCommissionChannels } from '@renderer/api/workBench/commission';
import to from 'await-to-js';
import { MessagePlugin } from 'tdesign-vue-next';
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';

const props = defineProps<{
  teamId?: string;
}>();

const route = useRoute();
const teamId = computed(() => props.teamId || route.query?.teamId as string);

const emit = defineEmits(['success']);

const visible = ref(false);
const type = ref('cbd');

const typeTitleMap = {
  cbd: '数字CBD',
  association: '数字协会',
  politics: '数字城市',
  member: '数字会员',
} as const;
const title = computed(() => typeTitleMap[type.value]);
const commissionRate = ref();

// 输入范围限制
const limit = ref({
  max: 5,
  min: 0,
});

// 输入范围矫正
const handleValidate = ({ error }) => {
  setTimeout(() => {
    if (error === 'below-minimum') {
      commissionRate.value = limit.value.min;
    } else if (error === 'exceed-maximum') {
      commissionRate.value = limit.value.max;
    }
  }, 800);
};

const onConfirm = async () => {
  const [err] = await to(setCommissionChannels({
    commission_rate: commissionRate.value,
    application_uuid: type.value,
  }, teamId.value));

  if (err) {
    MessagePlugin.error((err as any)?.response?.data?.message || err.message || '操作失败');
  }

  emit('success');
  visible.value = false;
};

const open = (params: { type: 'cbd' | 'association' | 'politics' | 'member'; maxRate: number }) => {
  visible.value = true;
  type.value = params.type;
  limit.value.max = params.maxRate;
};

defineExpose({
  open,
});
</script>

<template>
  <t-dialog
    v-model:visible="visible"
    width="480"
    class="commission-rate-setting-dialog"
    :close-on-overlay-click="false"
    :header="title"
    :on-confirm="onConfirm"
    :confirm-btn="{
      disabled: !commissionRate && commissionRate !== 0,
      theme: 'primary',
      text: '确定',
    }"
  >
    <template #closeBtn>
      <iconpark-icon name="iconerror" class="text-24 cursor-pointer" />
    </template>

    <div class="content">
      <p>每笔交易按照当前比例进行计算分账</p>

      <div class="input-wrap">
        <t-input-number
          v-model="commissionRate"
          theme="column"
          :min="limit.min"
          :max="limit.max"
          :step="0.01"
          :placeholder="`限制只能输入${limit.min}%-${limit.max}%`"
          class="w-full!"
          @validate="handleValidate"
        />
        <span>%</span>
      </div>
    </div>
  </t-dialog>
</template>

<style lang="scss">
.commission-rate-setting-dialog {
  .t-dialog {
    padding: 24px;
  }

  .content {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .input-wrap {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-kyy_color_text_1, #1A2139);
  }
}
</style>
