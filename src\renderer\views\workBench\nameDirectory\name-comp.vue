<template>
  <div class="orgbox">
    <vue3-tree-org
      :data="data"
      ref="treeOrgRef"
      :horizontal="horizontal"
      :collapsable="collapsable"
      :only-one-node="onlyOneNode"
      :clone-node-drag="false"
      :before-drag-end="beforeDragEnd"
      :default-expand-level="1"
      :disabled="false"
      :toolBar="false"
      :node-draggable="true"
      @on-node-drag="nodeDragMove"
      @on-node-drag-end="nodeDragEnd"
      @on-contextmenu="onMenus"
      @on-expand="onExpand"
      @on-node-dblclick="onNodeDblclick"
      @on-node-click="onNodeClick"
    >
      <template v-slot:expand="{ node }">
        <img class="expand-icon" @click="classHandleRun" :src="node.expand ? expandicon : expandicon2" alt="" />
      </template>
      <template v-slot="{ node }">
        <div
          class="node-boxss"
          @click="nodeClickRun(node)"
          v-if="node['$$data']['nodeType'] == 1"
          :class="{ 'top-line': node['$$data']['topLine'], 'bottom-line': node['$$data']['bottomLine'] }"
        >
          <div class="icon">
            <img :src="node['$$data']['image'] || depicon" alt="" />
          </div>
          <div class="namebox">
            <div class="pname">{{ node.label }}</div>
            <div class="pstext" v-if="node['$$data']['childrenEmpty']">（0位）</div>
            <div class="pstext" v-else>（{{ node.children.length }}位）</div>
          </div>
        </div>
        <div
          class="node-boxss"
          @click="nodeClickRun(node)"
          v-else-if="node['$$data']['nodeType'] == 3"
          :class="{ 'top-line': node['$$data']['topLine'], 'bottom-line': node['$$data']['bottomLine'] }"
        >
          <div class="namebox">
            <div class="nop">暂无成员</div>
          </div>
        </div>
        <div
          class="node-boxss"
          @click="nodeClickRun(node)"
          v-else
          :class="{ 'top-line': node['$$data']['topLine'], 'bottom-line': node['$$data']['bottomLine'] }"
        >
          <div class="icon">
            <img :src="node['$$data']['image']" alt="" />
          </div>
          <div class="namebox">
            <div class="cname">
              <div class="cname-text">
                {{ node.label }}
              </div>
              <div class="cname-tag" v-if="node['$$data']['tag']">{{ node['$$data']['tag'] }}</div>
            </div>
            <div class="pstext">{{ node['$$data']['post'] }}</div>
          </div>
        </div>
      </template>
    </vue3-tree-org>
    <div class="toolsss">
      <iconpark-icon name="iconpartner" class="iconbl" @click="pitch" />
      <div class="dr"></div>
      <iconpark-icon name="iconminus" class="iconmore" @click="zreduce" />
      <div class="scla">{{ ratio }}</div>
      <iconpark-icon name="iconadd" class="iconmore" @click="zadd" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { privateChatTools } from '../message/tool/service/type';

const expandicon = 'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/bigmarket/icon_left-c.svg';
const expandicon2 = 'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/bigmarket/icon_right-c.svg';
const depicon = 'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/bigmarket/icon_department.png';
const avicon = 'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/bigmarket/avatar.png';
const comicon = 'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/bigmarket/com_logo.png';
const data = ref({
  id: 1,
  label: '广州艾迪科技有限公司',
  nodeType: 1, // 1部门 2人员 3 无成员
  image: comicon, // 图标or头像
  children: [
    {
      id: 2,
      pid: 1, // 父id
      label: '产品研发部',
      nodeType: 1,
      topLine: 1, // 顶部线 children的第一个设置1， children只有一个不设置
      image: depicon,
      children: [
        { id: 26, pid: 2, label: '阿迪王', nodeType: 2, post: '演员', topLine: 1, image: avicon },
        { id: 28, pid: 2, label: '舔狗王', nodeType: 2, post: '演员', image: avicon },
        { id: 210, pid: 2, label: '欧颗哥', nodeType: 2, post: '演员', bottomLine: 1, image: avicon },
      ],
    },
    {
      id: 3,
      pid: 1,
      label: '客服部',
      nodeType: 1,
      image: depicon,
      children: [
        { id: 311, pid: 3, label: '客服一部', nodeType: 2, post: '客服', topLine: 1, image: avicon },
        { id: 312, pid: 3, label: '客服二部', nodeType: 2, post: '客服', bottomLine: 1, image: avicon },
      ],
    },
    {
      id: 9,
      pid: 1,
      label: '空部门',
      image: depicon,
      nodeType: 1,
      childrenEmpty: 1,
      children: [{ id: 911, pid: 9, label: '暂无成员', nodeType: 3 }],
    },
    {
      id: 4,
      pid: 1,
      label: '业务部',
      image: depicon,
      nodeType: 1,
      children: [
        {
          id: 411,
          pid: 4,
          nodeType: 2,
          label: '张李四三李四十几块上课李四还在弄李四李四',
          post: '业务',
          topLine: 1,
          tag: '负责人',
          image: avicon,
        },
        { id: 412, pid: 4, nodeType: 2, label: '李四', post: '业务', image: avicon },
        { id: 413, pid: 4, nodeType: 2, label: '王五', post: '业务', bottomLine: 1, image: avicon },
      ],
    },
    {
      id: 5,
      pid: 1,
      label: '测试部门',
      image: depicon,
      nodeType: 1,
      bottomLine: 1, // 底部线 children的最后一个设置1， children只有一个不设置
      children: [
        { id: 510, pid: 5, nodeType: 2, label: '水军', post: '测试', topLine: 1, tag: '管理员', image: avicon },
        { id: 511, pid: 5, nodeType: 2, label: '小红', post: '测试', image: avicon },
        { id: 512, pid: 5, nodeType: 2, label: '小娟', post: '测试', image: avicon },
        { id: 513, pid: 5, nodeType: 2, label: '小梅', post: '测试', bottomLine: 1, image: avicon },
      ],
    },
  ],
});
const onNodeClick = (node: any) => {
  console.log(node);
  // 获取当前缩放比例
  const currentScale = treeOrgRef.value?.scale || 1;
  // 根据缩放比例调整坐标
  clientXY.value.clientX = node.clientX / currentScale;
  clientXY.value.clientY = node.clientY / currentScale;
  const draggable = document.querySelector('.zm-draggable');
  if (draggable) {
    pitval.value = draggable.style.transform;
  }
};
const onNodeDblclick = (node: any) => {
  console.log(node);
};
const onExpand = (node: any) => {
  console.log(node);
  // classHandle();
};
const onMenus = (node: any) => {
  console.log(node);
};
const nodeDragMove = (node: any) => {
  console.log('拖拽移动中:', node);
  // 在拖拽过程中可以添加实时更新逻辑
};
const nodeDragEnd = (node: any) => {
  console.log('拖拽结束:', node);
  // 拖拽结束后重新计算位置
  setTimeout(() => {
    classHandleRun();
  }, 100);
};
const beforeDragEnd = (node: any) => {
  console.log('拖拽即将结束:', node);
  // 可以在这里进行拖拽验证
  return true; // 返回true允许拖拽，false阻止拖拽
};
const cloneNodeDrag = (node: any) => {
  console.log(node);
};
const horizontal = ref(true);
const collapsable = ref(true);
const onlyOneNode = ref(false);
const pitval = ref('');

const clientXY = ref({
  clientX: 0,
  clientY: 0,
});
const treeOrgRef = ref(null);
onMounted(() => {
  console.log('treeOrgRef.value', treeOrgRef.value);
  console.log(treeOrgRef.value.scale);
  // treeOrgRef.value.scale 当前缩放值
  // treeOrgRef.value.zoomOrgchart(0.5)
  // treeOrgRef.value.zoomOrgchart(-0.5) 缩小
  classHandleRun();
});
const zreduce = () => {
  treeOrgRef.value.zoomOrgchart(-0.1);
  // 缩放后重新计算拖拽相关的位置
  setTimeout(() => {
    updateDragPosition();
  }, 100);
};
const zadd = () => {
  if (treeOrgRef.value?.zoomPercent === '120%') {
    return;
  }
  treeOrgRef.value.zoomOrgchart(0.1);
  // 缩放后重新计算拖拽相关的位置
  setTimeout(() => {
    updateDragPosition();
  }, 100);
};
const ratio = computed(() => {
  return treeOrgRef.value?.zoomPercent || '100%';
});
const pitch = () => {
  const draggable = document.querySelector('.zm-draggable');
  if (draggable && pitval.value) {
    // 获取当前缩放比例
    const currentScale = treeOrgRef.value?.scale || 1;

    // 解析之前保存的transform值
    const transformMatch = pitval.value.match(/translate\(([^,]+),\s*([^)]+)\)/);
    if (transformMatch) {
      const x = parseFloat(transformMatch[1]);
      const y = parseFloat(transformMatch[2]);

      // 根据当前缩放比例调整位置
      const adjustedX = x * currentScale;
      const adjustedY = y * currentScale;

      draggable.style.transform = `translate(${adjustedX}px, ${adjustedY}px) scale(${currentScale})`;
    } else {
      // 如果无法解析，直接使用原值
      draggable.style.transform = pitval.value;
    }
  }
};
const classHandleRun = () => {
  setTimeout(() => {
    classHandle();
  }, 1);
};

// 更新拖拽位置的函数，处理缩放后的坐标问题
const updateDragPosition = () => {
  const draggable = document.querySelector('.zm-draggable');
  if (draggable && pitval.value) {
    const currentScale = treeOrgRef.value?.scale || 1;

    // 重新应用transform，考虑当前缩放比例
    const transformMatch = pitval.value.match(/translate\(([^,]+),\s*([^)]+)\)/);
    if (transformMatch) {
      const x = parseFloat(transformMatch[1]);
      const y = parseFloat(transformMatch[2]);

      // 根据缩放比例调整位置
      draggable.style.transform = `translate(${x}px, ${y}px) scale(${currentScale})`;
    }
  }
};

const classHandle = () => {
  const topLines = document.querySelectorAll('.top-line');
  topLines.forEach((el) => {
    // 找到祖父级元素
    const treeNode = el.closest('.tree-org-node');
    if (treeNode) {
      // 添加自定义类
      treeNode.classList.add('top-line-border');
    }
  });
  const bottomLines = document.querySelectorAll('.bottom-line');
  bottomLines.forEach((el) => {
    // 找到祖父级元素
    const treeNode = el.closest('.tree-org-node');
    if (treeNode) {
      // 添加自定义类
      treeNode.classList.add('bottom-line-border');
    }
  });
};
const nodeClickRun = (node: any) => {
  console.log(node);
};
</script>

<style lang="less">
.orgbox {
  height: 90vh;
  width: 100%;
  .expand-icon {
    width: 20px;
    height: 20px;
  }
}
.tree-org-node__expand:hover {
  background-color: none;
}
.tree-org-node__expand {
  box-shadow: none;
  background-color: transparent;
  border: none;
}
.horizontal .tree-org-node:not(:only-child):after {
  border-top: 1px solid #b2b1ff;
}
.horizontal .tree-org-node__children:before {
  border-top: 1px solid #b2b1ff;
  left: 30px;
  width: 50px;
}
.horizontal .tree-org-node:not(:first-child):before,
.horizontal .tree-org-node:not(:last-child):after {
  border-left: 1px solid #b2b1ff;
}
.horizontal .tree-org-node__children {
  padding-left: 80px;
}
// .horizontal .tree-org-node__children:before {
//   left: 16px;
// }
.horizontal.collapsable .tree-org-node.collapsed .tree-org-node__content:after {
  border-bottom: 1px solid #b2b1ff;
}
.zm-tree-org {
  background-color: none;
  background: url('http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/bigmarket/bg.png');
  background-size: cover;
}
.zm-tree-org .draggable {
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.tree-org-node__inner {
  margin-left: 8px;
  box-shadow: none !important;
}
.tree-org-node__content .tree-org-node__inner {
  box-shadow: none !important;
}
.node-boxss {
  display: flex;
  align-items: center;
  gap: 8px;
  .icon {
    img {
      width: 40px;
      height: 40px;
      border-radius: 50%;
    }
  }
  .namebox {
    min-width: 65px;
    .pname {
      display: -webkit-box;
      max-width: 140px;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      color: var(--text-kyy_color_text_1, #1a2139);
      text-overflow: ellipsis;
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      text-align: left;
    }
    .cname {
      display: flex;
      .cname-text {
        overflow: hidden;
        color: var(--text-kyy_color_text_1, #1a2139);
        text-align: center;
        text-overflow: ellipsis;
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
        text-align: left;
        display: -webkit-box;
        max-width: 140px;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        color: var(--text-kyy_color_text_1, #1a2139);
        text-overflow: ellipsis;
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
      }
      .cname-tag {
        display: flex;
        height: 20px;
        min-height: 20px;
        max-height: 20px;
        min-width: 60px;
        padding: 1px 8px;
        justify-content: center;
        align-items: center;
        gap: 10px;
        border-radius: var(--kyy_radius_tag_full, 999px);
        background: var(--kyy_color_tag_bg_success, #e0f2e5);
        color: var(--kyy_color_tag_text_success, #499d60);
        text-align: right;
        font-family: 'PingFang SC';
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px; /* 166.667% */
        float: left;
        margin-left: 4px;
      }
    }

    .nop {
      color: var(--text-kyy_color_text_2, #516082);
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
    }
    .pstext {
      color: var(--text-kyy_color_text_3, #828da5);
      text-align: left;
      font-family: 'PingFang SC';
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px; /* 166.667% */
    }
  }
}
.horizontal.collapsable .tree-org-node.collapsed .tree-org-node__content:after {
  border-bottom: none;
}
.horizontal .tree-org-node:not(:first-child):before,
.horizontal .tree-org-node:not(:last-child):after {
  border-top: none;
}
.horizontal .tree-org-node:not(:only-child):after {
  border-top: none;
}
.top-line-border::after {
  border-top: 1px solid #b2b1ff !important;
  border-top-left-radius: 12px;
  width: 10px !important;
}
.bottom-line-border::before {
  border-bottom: 1px solid #b2b1ff !important;
  border-bottom-left-radius: 12px;
  width: 10px !important;
}
.horizontal .tree-org-node:only-child:before {
  border-bottom: none !important;
  // top: 0px;
}
// .tree-org > .tree-org-node::first-child::before{
//     border-bottom: none;
// }

.toolsss {
  position: fixed;
  top: 160px;
  right: 48px;
  display: flex;
  padding: 8px;
  align-items: center;
  gap: 8px;
  position: absolute;
  right: 24px;
  top: 16px;
  border-radius: 8px;
  background: #fff;
  box-shadow: 0 3px 8px 0 rgba(0, 0, 0, 0.12);
  .iconbl {
    font-size: 20px;
    color: #1a2139;
    cursor: pointer;
  }
  .dr {
    display: flex;
    height: 16px;
    min-width: 1px;
    max-width: 1px;
    align-items: flex-start;
    gap: 4px;
    background: var(--divider-kyy_color_divider_light, #eceff5);
  }
  .iconmore {
    font-size: 20px;
    color: #828da5;
    cursor: pointer;
  }
  .scla {
    color: var(--text-kyy_color_text_1, #1a2139);
    text-align: center;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
    cursor: pointer;
    user-select: none;
  }
}
</style>



