<template>
  <div style="height: 100%">
    <work-area-head ref="zhixingHead" @onrefresh="refresh" />

    <router-view v-slot="{ Component }" class="zhixing-content" style="position: relative;">
      <keep-alive>
        <component :is="Component" ref="currComponentRef" />
      </keep-alive>
    </router-view>
  </div>
</template>

<script setup lang="ts">
import { watch, ref, nextTick, onMounted } from 'vue';
import WorkAreaHead from './components/WorkAreaHead.vue';
import { onMountedOrActivated } from "@/hooks/onMountedOrActivated";
import { useRoute, useRouter } from 'vue-router';
import { getZhixingTab } from './storage';
import {allRoutersUuid} from "@/constants";
import { zhixingRedPoint } from './util';
import { useZhixingStore } from './store/index';
import LynkerSDK from '@renderer/_jssdk';

const { ipcRenderer, shell } = LynkerSDK;
const zhixingStore = useZhixingStore();
const router = useRouter();
const route = useRoute();
const zhixingHead = ref(null);
const currComponentRef = ref(null);
const refresh = () => {
  currComponentRef.value.manualRefresh();
};

ipcRenderer.on("jump-zhixing", (event, value) => {
  const tab = getZhixingTab() || value.query.to;
  const path = `${tab}`;
  zhixingHead.value.setActive(tab);
  delete value.query.to;
  router.replace({path, query: value.query});
  nextTick(() => {
    refresh();
  });
});

// 日程变更
ipcRenderer.on("send-refresh-schedule", async () => {
  console.log('日程通知',7899);

  // 使用await先统计更新zhixingStore中的count数据，防止组件中使用到没有及时更新的zhixingStore count数据
  await updateRedPoint();
  refresh();
});

// 笔记本标签变更消息
ipcRenderer.on("notebook-label-change-message", () => {
  console.log('审批通知',78999);

});

// 稍后处理变更
ipcRenderer.on("update-zhixing-internal-count",  async () => {
  console.log('待办通知',789);

  // 使用await先统计更新zhixingStore中的count数据，防止组件中使用到没有及时更新的zhixingStore count数据
  await updateRedPoint();
  currComponentRef.value.updateRedPointNumber();
})

// 笔记更新
ipcRenderer.on("note-update-message-count", () => {
  nextTick(() => {
    console.log('笔记更新-------');

    refresh();
  });
})

// 更新红点
const updateRedPoint = async () => {
  // const countObj = await zhixingRedPoint(['notice','approval','later', 'manifestAll']);
  console.log('====>updateRedPoint');

  const countObj = await zhixingRedPoint();
  // 更新待办红点
  console.log('====>updateRedPoint', countObj);
  zhixingStore.setToDoCount(countObj.manifestAll + countObj.approval + countObj.later + countObj.notice + countObj.activity);
  // console.log('countObj****',countObj);
  zhixingStore.setManifestCount(countObj.notice);
  zhixingStore.setRemindCount(countObj.notice);
  zhixingStore.setActivityCount(countObj.activity);
  // 更新侧边红点
  ipcRenderer.invoke('update-zhixing-count');
}

onMountedOrActivated(() => {
  if (route.query.to) {
    nextTick(() => {
      const tab = getZhixingTab() || route.query.to;
      const path = `zhixing/${tab}`;
      zhixingHead.value.setActive(tab);
      delete route.query.to;
      router.replace({path, query: route.query});
    })
  }
});

onMounted(() => {
  LynkerSDK.ipc.handleRenderer('zhixing-is-inited', async () => {
    console.log('zhixing-is-inited');
    return true;
  });
});

</script>

<style lang="less" scoped>
@import '../../style/mixins.less';
.zhixing-content {
  height: calc(100% - 40px);
}
</style>
