# 组织收款

## 组织收款1.0（25/04/8 - 25/04/28）

> [TAPD](https://www.tapd.cn/69781318/prong/iterations/card_view/1169781318001001261?q=7cecf03c0c0c6b30136c0956780bba70) / [原型](https://app.mockplus.cn/run/prototype/cQlhQUX3hg/ud2xAIs_r/8OKCXV_gXq5?allowShare=1&cps=expand&ha=1&isShare=false&sr=collapse) / [UI](https://www.figma.com/design/OKIBMDgretWDhsvLUIlyOl/%E7%BB%84%E7%BB%87%E6%94%B6%E6%AC%BE-%E6%A1%8C%E9%9D%A2%E7%AB%AF?node-id=314-38552&m=dev)

### 项目描述

> TODO 项目描述

---

## 流程与状态说明（对齐 PRD）

### 总体流程

共三步：商户入网、商户实名、商户分账

- 入口页：SettlementJoin.vue（已入网显示入网信息、未入网显示申请按钮）
- 申请页：JoinApply.vue（包含入网申请表单、审核状态、实名认证、分账等部分）
- 状态组件：VerifyStatus.vue（渲染所有状态页面）
- 入网表单：ApplyForm.vue

#### 1. 组织认证阶段

- **未认证/已过期**：需先完成组织认证，弹出组织认证弹窗。
- **认证中**：弹出认证中弹窗。
- **认证失败**：弹出认证失败弹窗。
- **已认证**：进入商户入网阶段。

#### 2. 商户入网阶段

- **未入网**：
  - 显示"需先开通商户入网以及商户分账业务后，才可进行交易收款"，显示"申请"按钮。
  - 点击"申请"后，若组织已认证，则新开 Tab 进入商户入网申请页面。
  - 填写商户信息，提交后进入电子合同签约。
  - 电子合同签约后，进入审核中。
- **审核中**：
  - 页面提示"审核人员会在 1~3 工作日内完成审核，审核结果会通过'小秘书'进行通知"。
  - 可点击刷新按钮重新获取状态。
- **审核失败**：
  - 显示失败原因。
  - 可点击"去修改"进入入网信息修改页面，回显原内容，重新提交后重新走电子合同签约和审核流程。
- **审核通过**：
  - 显示审核通过页面，回显申请内容。
  - 下一步进入商户实名。

#### 3. 商户实名阶段

- **未实名**：
  - 显示微信、支付宝实名认证二维码，认证标识均为"未认证"。
  - "我已核实商户号"按钮：点击后查询实名状态，未实名则弹 toast，已实名则状态变为"已实名"且按钮置灰。
  - 商户号为入网成功后获得的拉卡拉商户号。
  - 下一步需微信、支付宝均实名认证后才可进入分账业务。
- **已实名**：
  - 显示"已实名"，按钮不可点击。

#### 4. 分账业务阶段

- **未开通**：
  - 商户实名后，进入分账业务开通页面。
  - 提交后进入分账审核中。
- **审核中**：
  - 等待拉卡拉人工审核。
- **审核失败**：
  - 显示失败原因，可重新提交。
- **审核通过/已开通**：
  - 流程结束，显示入网成功信息。

---

### 状态流转节点与条件（参考 useMerchantFlow.ts）

| 阶段         | 关键状态字段                  | 状态值 | 说明                         | 下一步流转                |
|--------------|-------------------------------|--------|------------------------------|---------------------------|
| 组织认证     | orgAuthDetail.auth            | 0      | 未认证/已过期                | 弹出组织认证弹窗          |
|              |                               | 1      | 已认证                      | 进入商户入网              |
| 商户入网     | merchantStore.isExist         | false  | 未入网                      | 进入入网申请              |
|              | status.is_open_merchant | 0 | 未入网信息                  | 填写入网信息              |
|              | status.is_open_merchant | 1 | 入网审核中                  | 等待审核                  |
|              | status.is_open_merchant | 2 | 已入网                      | 进入签约/实名             |
|              | status.is_open_merchant | 3 | 入网失败                    | 重新提交                  |
| 签约         | status.apply_status | 1  | 待签约                      | 跳转签约                  |
|              | status.apply_status | 2  | 签约成功                    | 进入实名                  |
|              | status.apply_status | 3  | 入网失败                    | 重新提交                  |
| 商户实名     | status.is_alipay_merchant<br>status.is_wechat_merchant | 1/1 | 已实名 | 进入分账业务 |
|              |                               | 0/0    | 未实名                      | 跳转实名                  |
|              |                               | 1/0 或 0/1 | 部分实名                  | 实名审核中                |
| 分账业务     | status.is_separate_accounts | 0 | 未分账（实名后）           | 分账审核中                |
|              | status.is_separate_accounts | 1 | 分账审核中                  | 等待审核                  |
|              | status.is_separate_accounts | 2 | 分账已开通                  | 流程结束                  |
|              | status.is_separate_accounts | 3 | 分账审核失败                | 重新提交                  |

> **优先级顺序：分账 > 实名 > 签约 > 入网 > 成功**  
> 其中，分账状态优先于实名、签约、入网等其他节点。

---

### Mermaid 流程图（对齐 useMerchantFlow.ts 最新实现）

```mermaid
sequenceDiagram
  participant 用户
  participant 业务中台
  participant 拉卡拉

  用户->>业务中台: 访问收款入口
  业务中台->>业务中台: 校验组织认证状态
  alt 未认证/已过期
    业务中台-->>用户: 显示认证弹窗
    用户->>业务中台: 提交认证材料
    业务中台->>业务中台: 完成认证
  end

  业务中台->>拉卡拉: 发起商户入网
  alt 首次入网
    拉卡拉-->>用户: 返回入网表单
    用户->>拉卡拉: 提交入网信息
    拉卡拉->>拉卡拉: 生成电子合同
    拉卡拉-->>用户: 跳转签约
    用户->>拉卡拉: 完成签约
  else 审核失败
    拉卡拉->>业务中台: 返回失败原因
    业务中台-->>用户: 重新提交入网信息
  end

  拉卡拉->>拉卡拉: 执行实名认证
  alt 未完成实名
    拉卡拉-->>用户: 展示双渠道二维码
    用户->>拉卡拉: 完成微信认证
    用户->>拉卡拉: 完成支付宝认证
  end

  拉卡拉->>拉卡拉: 发起分账审核
  alt 审核通过
    拉卡拉->>业务中台: 通知流程完成
    业务中台-->>用户: 显示入网成功
  else 审核失败
    拉卡拉->>业务中台: 返回失败原因
    业务中台-->>用户: 重新提交申请
  end
```

---

### 详细流程节点说明（补充 PRD 细节）

- 所有状态页面均由 VerifyStatus.vue 渲染，页面跳转均为新 Tab。
- 电子合同签约状态通过 getApplyStatus 查询，apply_status=1 为待签约，2 为签约成功，3 为签约失败。
- 商户实名需微信、支付宝均认证通过。
- 分账业务审核失败可重新提交，成功后流程结束。
- 极端情况：若页面状态已变更（如分账业务已开通），需刷新页面显示最新信息。
- 所有失败原因均需回显，toast 提示需与 PRD 保持一致。

---

### 备注

- 具体接口申明见 src/renderer/api/workBench/merchant.ts
- TypeScript 数据模型见 src/renderer/api/workBench/merchant.model.ts
- 主要页面：
  - src/renderer/views/workBench/merchant/SettlementJoin.vue
  - src/renderer/views/workBench/merchant/JoinApply.vue
    - src/renderer/views/workBench/merchant/components/ApplyForm.vue
    - src/renderer/views/workBench/merchant/components/VerifyStatus.vue
- "小秘书"消息由后端处理，无需前端处理
- 详细交互、极端情况、页面跳转等请参考 PRD 文档

---

## 提现管理 PRD 文档

### 业务流程

提现账户的开通与展示依赖于商户入网状态，整体流程与商户入网流程保持一致：

1. **未入网**：
   - 提现账户页面显示"没有账户"，并显示"申请"按钮。
   - 点击"申请"后，判断组织认证情况：
     - 未认证/已过期：侧边弹出组织认证弹窗。
     - 认证中：侧边弹出认证中弹窗。
     - 认证失败：侧边弹出认证失败弹窗。
     - 已认证：判断是否已商户入网。
   - 若未入网，则新开 Tab 进入商户入网页面。
2. **已入网**：
   - 若已入网，弹出 toast 提示"当前组织已成功入网"，并刷新页面显示提现账户信息。

### 备注

- 提现账户的入网状态判断逻辑与 SettlementJoin.vue 保持一致。
- 组织认证、商户入网等弹窗交互与主流程一致。
- 具体页面与组件可根据实际业务需求复用商户入网相关实现。

---

## 前端调试与 mock 场景说明

支持在开发环境下通过 mock 场景一键切换收款流程各节点，便于前端独立调试和联调。mock 仅在开发环境下生效，生产环境无效。

### 配置方法

- 配置文件：`src/renderer/views/workBench/receipt/debug.ts`
- 通过 `useDebugState().isDebugEnabled.value = true` 启用 mock。
- 切换场景：`useDebugState().debugConfig.value.scene = '分账审核中'`（scene 仅可为下方 key）。
- 如需自定义状态，直接设置 `status` 字段，优先级高于 scene。

### 可用 mock 场景 key

- 未入网
- 入网审核中
- 入网失败
- 签约中
- 入网成功
- 部分实名
- 实名成功
- 分账审核中
- 分账失败
- 分账成功

### 注意事项

- mock 仅在开发环境下生效，生产环境下无论如何设置都不会影响真实数据。
- `status` 字段优先级高于 `scene`，如两者同时存在，优先使用 `status`。
- `scene` 字段类型受限于 mock 场景 key，类型安全，IDE 智能提示。
- 业务代码无需改动，所有 getStatus 调用点自动感知 mock 配置。

### 示例

实际使用时，只需在 `src/renderer/views/workBench/receipt/debug.ts` 文件顶部修改相关变量：

```ts
// debug.ts 示例

// 启用 mock
const defaultIsDebugEnabled = true;

// 切换到"分账审核中"场景
const defaultDebugConfig = {
  scene: '分账审核中',
  // 或自定义状态，优先级高于 scene
  // status: { is_open_merchant: '2', apply_status: '2', ... }
};
```

无需在业务代码中调用 setDebugEnabled 或 useDebugState，所有 getStatus 调用点会自动感知 debug 配置。

如需扩展 mock 场景，只需在 `merchantMockSceneMap` 中新增 key 并同步类型。
