<script setup lang="tsx">
import { getCommissionList, notifyOpenMerchant } from '@renderer/api/workBench/commission';
import to from 'await-to-js';
import { computed, onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
import { CommissionListItem } from '@renderer/api/workBench/commission.model';
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
import AdvancedFilter from '@renderer/components/AdvancedFilter.vue';
import Empty from '@renderer/components/common/Empty.vue';
import JoinDigitalPlatformDrawer from '@renderer/views/digital-platform/components/join-drawer.vue';
import debounce from 'lodash/debounce';
import CommissionBindDialog from './CommissionBindDialog.vue';

const props = defineProps<{
  teamId: string;
}>();

const route = useRoute();
const teamId = computed(() => props.teamId || route.query?.teamId as string);

const commissionBindDialogVisible = ref(false);
const commissionRow = ref<CommissionListItem>({} as CommissionListItem);
const joinDigitalPlatformDrawerRef = ref(null);

const typeList = [
  { label: '数字商协', value: 'member' },
  { label: '数字政企', value: 'government' },
  { label: '数字CBD', value: 'cbd' },
  { label: '数字社群', value: 'association' },
];
const isSeparateAccountsList = ref([
  { label: '是', value: '2' },
  { label: '否', value: '1' },
]);

const searchFormRef = ref(null);
const searchFormData = ref({
  application_uuid: 'member',
  is_separate_accounts: undefined,
  keyword: undefined,
});

const handleSwitchUuid = (value: string) => {
  searchFormData.value.application_uuid = value;
  getList();
};

const columns = ref([
  // { colKey: 'type', cell: 'type', title: '类型', width: 200 },
  { colKey: 'team_name', title: '组织名称', ellipsis: true },
  { colKey: 'isOpenMerchant', cell: 'isOpenMerchant', title: '是否入网', width: 144 },
  { colKey: 'status', cell: 'status', title: '关联状态', width: 144 },
  { colKey: 'action', cell: 'action', title: '操作', width: 160 },
]);
const tableData = ref<CommissionListItem[]>([]);
const loading = ref(true);

// const typeColorMap = {
//   member: 'color-[#FC7C14]',
//   cbd: 'color-[#4D5EFF]',
//   association: 'color-[#ED565C]',
// };

const tagClassMap = {
  0: 'color-[#516082]! bg-[#ECEFF5]!',
  1: 'color-[#FC7C14]! bg-[#FFE5D1]!',
  2: 'color-[#D92F4D]! bg-[#FBDDE3]!',
  3: 'color-[#FC7C14]! bg-[#FFE5D1]!',
  4: 'color-[#D92F4D]! bg-[#FBDDE3]!',
  5: 'color-[#499D60]! bg-[#E0F2E5]!',
};

const getList = async () => {
  loading.value = true;
  const [err, res] = await to(getCommissionList({
    ...searchFormData.value,
    application_uuid: searchFormData.value.application_uuid,
  }, teamId.value));

  loading.value = false;
  if (err) return;

  tableData.value = res.data.data.list.map((item) => {
    // const { img, name } = filterImg({ uuid: item.application_uuid });
    const isOpenMerchant = item.is_open_merchant === 2;

    return {
      ...item,
      // type: name,
      // typeImg: img,
      // typeColor: typeColorMap[item.application_uuid],
      isOpenMerchant,
      isOpenMerchantText: isOpenMerchant ? '已入网' : '未入网',
      isOpenMerchantColor: isOpenMerchant ? 'color-[#499D60]' : 'color-[#516082]',
      statusTag: {
        text: { 0: '未关联', 1: '待确定', 2: '已拒绝', 3: '待审核', 4: '审核失败', 5: '已关联' }[item.status],
        class: `${tagClassMap[item.status]} font-600! h-24!`,
      },
    };
  });
};

const handleSearch = debounce((value: string) => {
  searchFormData.value.keyword = value;
  getList();
}, 400);

onMounted(() => {
  getList();
});

const handleReset = () => {
  searchFormData.value = {
    application_uuid: 'member',
    is_separate_accounts: undefined,
    keyword: undefined,
  };

  getList();
};

// 关联
const handleBind = async (row: CommissionListItem) => {
  commissionRow.value = row;
  commissionBindDialogVisible.value = true;
};

// 重新关联
const handleRebind = (row: CommissionListItem) => {
  handleBind(row);
};

// 重新审核
const handleReAudit = (row: CommissionListItem) => {
  handleBind(row);
};

// 拒绝原因
const handleReason = (row: CommissionListItem) => {
  const dialog = DialogPlugin.confirm({
    header: '拒绝原因',
    body: `原因：${row.message}`,
    confirmBtn: {
      content: '关闭',
      theme: 'default',
    },
    closeBtn: false,
    cancelBtn: null,
    onConfirm: () => {
      dialog.destroy();
    },
  });
};

// 通知
const handleNotify = (row: CommissionListItem) => {
  const dialog = DialogPlugin.confirm({
    header: '提示',
    theme: 'info',
    body: `是否通知“${row.team_name}”进行入网操作？`,
    confirmBtn: '通知',
    closeBtn: false,
    onConfirm: async () => {
      const [err] = await to(notifyOpenMerchant({ relation_team_id: row.relation_team_id }, teamId.value));
      if (err) {
        MessagePlugin.error((err as any).response.data.message);
        return;
      }

      getList();
      dialog.destroy();
    },
  });
};
</script>

<template>
  <div class="page-wrap">
    <div class="page-header">
      <div class="filter-wrap">
        <div class="flex flex-1">
          <t-input
            v-model="searchFormData.keyword"
            placeholder="搜索组织名称"
            class="w-304!"
            @change="handleSearch"
          >
            <template #prefixIcon>
              <t-icon name="search" />
            </template>
          </t-input>

          <AdvancedFilter :in-search="!!searchFormData.is_separate_accounts" @reset="handleReset" @confirm="getList">
            <template #content>
              <t-form ref="searchFormRef" :data="searchFormData" label-align="top">
                <t-form-item name="name" label="是否入网">
                  <t-select v-model="searchFormData.is_separate_accounts" :options="isSeparateAccountsList" clearable />
                </t-form-item>
              </t-form>
            </template>
          </AdvancedFilter>
        </div>

        <t-tooltip placement="bottom-right" :show-arrow="false" :overlay-inner-style="{ maxWidth: '300px' }">
          <div class="desc">
            关联说明
            <t-icon name="help-circle" class="text-16 color-text-3 cursor-pointer" />
          </div>
          <template #content>
            <p>1、渠道展示为自身组织所关联到其他的数字平台（不包括数字城市）</p>
            <p>2、申请关联后，自身组织作为分账方，其他数字平台作为分佣方。</p>
            <p>3、自身可以将店铺的商品推到所关联的数字平台进行展示，后续在数字平台上产生交易后，自身需要支付对应数字平台所设置的佣金作为报酬</p>
          </template>
        </t-tooltip>
      </div>

      <div class="flex gap-8 mb-16">
        <t-button
          v-for="item in typeList"
          :key="item.value"
          :theme="searchFormData.application_uuid === item.value ? 'primary' : 'default'"
          class="font-600!"
          @click="handleSwitchUuid(item.value)"
        >
          {{ item.label }}
        </t-button>
      </div>
    </div>

    <t-table
      :loading="loading"
      row-key="id"
      :data="tableData"
      :columns="columns"
      :max-height="500"
    >
      <!-- <template #type="{ row }">
        <div class="type-wrap">
          <img :src="row.typeImg" class="w-20 h-20" alt="icon">
          <span :class="row.typeColor">{{ row.type }}</span>
        </div>
      </template> -->
      <template #isOpenMerchant="{ row }">
        <span :class="row.isOpenMerchantColor">
          {{ row.isOpenMerchantText }}
        </span>
      </template>
      <template #status="{ row }">
        <t-tag shape="round" :class="row.statusTag.class">
          {{ row.statusTag.text }}
        </t-tag>
      </template>
      <template #action="{ row }">
        <div class="flex gap-8">
          <!-- 已入网 -->
          <template v-if="row.isOpenMerchant">
            <t-button
              v-if="row.status === 0"
              theme="primary"
              variant="text"
              @click="handleBind(row)"
            >
              关联
            </t-button>
            <t-button
              v-if="row.status === 2"
              theme="primary"
              variant="text"
              @click="handleRebind(row)"
            >
              重新关联
            </t-button>
            <t-button
              v-if="row.status === 4"
              theme="primary"
              variant="text"
              @click="handleReAudit(row)"
            >
              重新审核
            </t-button>
            <t-button
              v-if="row.status === 4"
              theme="primary"
              variant="text"
              @click="handleReason(row)"
            >
              原因
            </t-button>
          </template>
          <!-- 未入网 -->
          <t-button
            v-else-if="row.status === 0"
            theme="primary"
            variant="text"
            @click="handleNotify(row)"
          >
            通知
          </t-button>
        </div>
      </template>

      <template #empty>
        <div class="empty-wrap">
          <Empty tip="暂未加入数字平台">
            <template #bottom>
              <t-button theme="primary" class="mt-16!" @click="joinDigitalPlatformDrawerRef.onOpen()">
                加入数字平台
              </t-button>
            </template>
          </Empty>
        </div>
      </template>
    </t-table>

    <CommissionBindDialog
      v-model:visible="commissionBindDialogVisible"
      :team-id="teamId"
      :row="commissionRow"
      @refresh="getList"
    />

    <JoinDigitalPlatformDrawer ref="joinDigitalPlatformDrawerRef" />
  </div>
</template>

<style scoped lang="less">
.page-wrap {
  width: 100%;
  height: 100%;
}

.page-header {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.filter-wrap {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .desc {
    display: flex;
    align-items: center;
    gap: 4px;
    color: var(--text-kyy_color_text_1, #1A2139);
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
  }
}

// .type-wrap {
//   height: 24px;
//   display: inline-flex;
//   padding: 0px 8px;
//   align-items: center;
//   gap: 4px;
//   border-radius: 4px;
//   background: var(--bg-kyy_color_bg_deep, #F5F8FE);
// }

:deep(.t-button--variant-text.t-button--theme-primary) {
  padding: 4px !important;
  height: 30px !important;
  color: var(--kyy_color_switch_brand_default, #4D5EFF) !important;

  &:hover {
    border-radius: 4px;
    background-color: var(--bg-kyy_color_bg_list_foucs, #E1EAFF) !important;
  }
}
</style>
