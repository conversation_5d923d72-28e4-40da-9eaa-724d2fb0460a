import { defineConfig } from "unocss";
import presetUno from "@unocss/preset-uno";
import transformerDirectives from "@unocss/transformer-directives";
import presetRemToPx from "@unocss/preset-rem-to-px";

// https://unocss.dev/
export default defineConfig({
  exclude: [
    "node_modules",
    "dist",
    ".git",
    ".electron-vite",
    "config",
    "customTypes",
    "rootLib",
    "src/main"
  ],
  presets: [
    presetUno({ dark: "class" }),
    // 将所有工具类单位由rem转为px，例如：w-4 代表 width: 4px（默认为 width: 1rem）
    presetRemToPx({ baseFontSize: 4 })
  ],
  transformers: [transformerDirectives()],
  shortcuts: {
    "wh-full": "w-full h-full",
    "flex-center": "flex justify-center items-center",
    "flex-col-center": "flex-center flex-col",
    "flex-x-center": "flex justify-center",
    "flex-y-center": "flex items-center",
    "i-flex-center": "inline-flex justify-center items-center",
    "i-flex-x-center": "inline-flex justify-center",
    "i-flex-y-center": "inline-flex items-center",
    "flex-col": "flex flex-col",
    "flex-col-stretch": "flex-col items-stretch",
    "i-flex-col": "inline-flex flex-col",
    "i-flex-col-stretch": "i-flex-col items-stretch",
    "flex-1-hidden": "flex-1 overflow-hidden",
    "nowrap-hidden": "whitespace-nowrap overflow-hidden",
    "ellipsis-text": "nowrap-hidden text-ellipsis",
    "transition-base": "transition-all duration-300 ease-in-out",
    cursor: "cursor-pointer select-unset"
  }
});
