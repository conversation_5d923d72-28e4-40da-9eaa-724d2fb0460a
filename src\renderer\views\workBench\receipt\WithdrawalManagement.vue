<script setup lang='ts'>
import { ref, computed } from 'vue';
import OrgAuth from '@renderer/components/orgAuth/orgAuth.vue';
import { onMountedOrActivated } from '@renderer/hooks/onMountedOrActivated';
import { useRoute } from 'vue-router';
import { useMerchantStore } from '@renderer/store/modules/merchant';
import { useTeamAuthStatus } from './hooks/useMerchant';
import useMerchantFlow from './hooks/useMerchantFlow';
import WithdrawalAccount from './components/WithdrawalAccount.vue';
import WithdrawalPassword from './components/WithdrawalPassword.vue';

const props = defineProps({
  team: {
    type: Object,
    default: () => ({}),
  },
});

const route = useRoute();
const teamId = computed(() => route.query?.teamId as string || props?.team?.teamId || '');

const {
  loading: authLoading,
  orgAuthDetail,
  orgAuthVisible,
  fetchTeamAuthStatus,
} = useTeamAuthStatus(teamId);
const orgAuthShowType = computed(() => (orgAuthDetail.value?.auth === 0 ? 'edit' : 'detail'));

// 入网流程状态流转
const { handleApplyClickFlow } = useMerchantFlow();

const merchantStore = useMerchantStore();

// 是否显示申请按钮
const showApplyBtn = computed(() => {
  if (!merchantStore.isExist) return true;
  if (merchantStore.status?.is_open_merchant === '2') return false;
  return true;
});

const init = async () => {
  await merchantStore.checkExist(teamId.value);
  if (merchantStore.isExist) {
    await merchantStore.getStatus(teamId.value);
  }
};

onMountedOrActivated(() => {
  init();
});

const activeTab = ref(route.query?.tab as string || 'account');
const tabList = [
  { label: '提现账户', value: 'account' },
  { label: '提现密码', value: 'password' },
];

const handleTabClick = (value: string) => {
  activeTab.value = value;
};

// 申请
const handleApplyClick = async () => {
  await handleApplyClickFlow({
    teamId: teamId.value,
    fetchTeamAuthStatus,
    orgAuthVisible,
    loading: authLoading,
  });
};
</script>

<template>
  <div class="page-container">
    <div class="page-header">
      <div
        v-for="tab in tabList"
        :key="tab.value"
        class="tab-item"
        :class="{ active: activeTab === tab.value }"
        @click="handleTabClick(tab.value)"
      >
        {{ tab.label }}
      </div>
    </div>

    <div class="page-content">
      <div v-if="showApplyBtn" class="page-empty">
        <img src="@/assets/workbench/bg_withdrawal.png" alt="" class="w-324 h-185">
        <div class="tip-wrap">
          <p class="title">提现账户</p>
          <p>需先开通商户入网后，才可进行资金提现</p>
        </div>
        <t-button
          theme="primary"
          class="mt-12 min-w-80"
          :loading="authLoading"
          @click="handleApplyClick"
        >
          申请
        </t-button>
      </div>

      <template v-else>
        <WithdrawalAccount v-if="activeTab === 'account'" :team-id="teamId" />
        <WithdrawalPassword v-if="activeTab === 'password'" :team-id="teamId" />
      </template>
    </div>

    <!-- 组织认证 -->
    <OrgAuth
      v-if="orgAuthVisible"
      v-model:visible="orgAuthVisible"
      :show-type="orgAuthShowType"
      :region="orgAuthDetail?.region || 'CN'"
      :team-id="orgAuthDetail?.teamId || ''"
      :org-type="orgAuthDetail?.orgType"
      @success="orgAuthVisible = false"
    />
  </div>
</template>

<style lang='less' scoped>
.page-container {
  width: 100%;
  height: calc(100vh - 44px);
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 8px;
}

.page-header {
  display: flex;
  padding: 0px 16px;
  gap: 32px;
  border-radius: 8px;
  border: 1px solid var(--divider-kyy_color_divider_light, #ECEFF5);
  background: var(--bg-kyy_color_bg_light, #FFF);
  .tab-item {
    display: flex;
    height: 56px;
    align-items: center;
    color: var(--text-kyy_color_text_1, #1A2139);
    text-align: center;
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
    cursor: pointer;
    position: relative;
    &.active {
      color: var(--brand-kyy_color_brand_default, #4D5EFF);
      font-weight: 600;
      &::before {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        bottom: 0;
        content: '';
        display: block;
        width: 16px;
        height: 3px;
        flex-shrink: 0;
        background: var(--brand-kyy_color_brand_default, #4D5EFF);
        border-radius: 1.5px;
      }
    }
  }
}

.page-content {
  padding: 16px;
  margin-bottom: 4px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  border: 1px solid var(--divider-kyy_color_divider_light, #ECEFF5);
  background: var(--bg-kyy_color_bg_light, #FFF);
}

.page-empty {
  width: 100%;
  text-align: center;
  .tip-wrap {
    margin-top: 32px;
    display: flex;
    flex-direction: column;
    gap: 4px;
    color: var(--text-kyy_color_text_2, #516082);
    text-align: center;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    .title {
      color: var(--text-kyy_color_text_1, #1A2139);
    }
  }
}
</style>
