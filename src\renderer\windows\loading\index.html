<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>loading</title>
    <style>
      html, body {
        margin: 0;
        padding: 0;
      }
      #app {
        position: fixed;
        height: 100%;
        width: 100%;
        display: flex;
        flex-direction: column;
        gap: 8px;
        justify-content: center;
        align-items: center;
        background-color: rgba(255,255,255,.7);
      }
      /* HTML: <div class="loader"></div> */
      /* HTML: <div class="loader"></div> */
      .loader {
        width: 28px;
        padding: 8px;
        aspect-ratio: 1;
        border-radius: 50%;
        background:  #4C5EFF;
        --_m:
          conic-gradient(#0000 10%,#000),
          linear-gradient(#000 0 0) content-box;
        -webkit-mask: var(--_m);
                mask: var(--_m);
        -webkit-mask-composite: source-out;
                mask-composite: subtract;
        animation: l3 1s infinite linear;
      }
      @keyframes l3 {to{transform: rotate(1turn)}}
      .loader-text {
        font-family: PingFang SC;
        font-size: 14px;
        font-weight: 400;
        line-height: 22px;
        text-align: left;
      }
    </style>
</head>

<body>
    <div id="app">
      <span class="loader"></span>
      <div class="loader-text">加载中...</div>
    </div>
</body>

</html>
